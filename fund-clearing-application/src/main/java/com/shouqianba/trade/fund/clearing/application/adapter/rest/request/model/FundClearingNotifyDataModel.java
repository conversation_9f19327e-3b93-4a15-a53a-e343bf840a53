package com.shouqianba.trade.fund.clearing.application.adapter.rest.request.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

@Getter
@ToString
@SuperBuilder(toBuilder = true)
@Jacksonized
public class FundClearingNotifyDataModel {

    @JsonProperty("brand_id")
    private String brandId;

    @JsonProperty("date")
    private String date;

    @JsonProperty("action_id")
    private String actionId;

    @JsonProperty("client_sn")
    private String clientSn;

    @JsonProperty("status")
    private Integer status;

    @JsonProperty("finish_time")
    private Long finishTime;

    @JsonProperty("fail_msg")
    private String failMsg;

    @JsonProperty("clearing_result_file")
    private String clearingResultFile;
}