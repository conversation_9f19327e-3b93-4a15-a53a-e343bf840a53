package com.shouqianba.trade.fund.clearing.application.biz.impl.handler.impl;

import com.google.common.collect.Lists;
import com.shouqianba.trade.fund.clearing.api.request.model.FundBillSortModel;
import com.shouqianba.trade.fund.clearing.api.request.model.FundPoolCursorModel;
import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.AbstractBehaviorTreeStrategy;
import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.BaseEventStrategy;
import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.context.impl.FundBatchPartialClearingEventContext;
import com.shouqianba.trade.fund.clearing.common.exception.FundClearingBizException;
import com.shouqianba.trade.fund.clearing.common.template.ExternalInvokeWithTransactionTemplate;
import com.shouqianba.trade.fund.clearing.common.template.InvokeProcessor;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.EventDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.EventAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.enums.EventTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.vo.EventContentPartialClearingVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.FundBatchDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.model.FundBatchAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.FundBillDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.FundBillAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.query.FundBillAggrQuery;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.settle.SettleClient;
import com.shouqianba.trade.fund.clearing.infrastructure.support.service.ClearingAccountSupportService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> Date: 2025/04/22 Time: 02:59AM
 */
@Slf4j
@Component
public class FundBatchPartialClearingStrategy extends AbstractBehaviorTreeStrategy<FundBatchPartialClearingEventContext> {

    private static final EventTypeEnum EVENT_TYPE = EventTypeEnum.PARTIAL_CLEARING;
    private static final int DEFAULT_PAGE_SIZE = 100;

    @Resource
    private FundBillDomainRepository fundBillDomainRepository;
    @Resource
    private FundBatchDomainRepository fundBatchDomainRepository;
    @Resource
    private SettleClient settleClient;
    @Resource
    private ClearingAccountSupportService clearingAccountSupportService;
    @Resource
    private EventDomainRepository eventDomainRepository;

    @Override
    protected Pair<EventTypeEnum, BaseEventStrategy<FundBatchPartialClearingEventContext>> register() {
        return Pair.of(EVENT_TYPE, this);
    }

    @Override
    protected IndependentEventStrategyRunnable getIndependentEventStrategyRunnable(
            FundBatchPartialClearingEventContext context) {
        return new FundBillEntryRunnable(context);
    }

    public class FundBillEntryRunnable extends IndependentEventStrategyRunnable {
        private final FundBatchPartialClearingEventContext context;

        public FundBillEntryRunnable(FundBatchPartialClearingEventContext context) {
            this.context = context;
        }

        @Override
        public void run() {
            InvokeProcessor.processExternalInvokeWithTransaction(context,
                    new ExternalInvokeWithTransactionTemplate<>() {

                        @Override
                        protected boolean preInvokeExternal(FundBatchPartialClearingEventContext context) {
                            return Boolean.TRUE;
                        }

                        @Override
                        protected void invokeExternal(FundBatchPartialClearingEventContext context) {
                            EventAggrRoot eventAggrRoot = context.getEventAggrRoot();

                            // 查询批次
                            FundBatchAggrRoot fundBatchAggrRoot =
                                    fundBatchDomainRepository.query(context.genFundBatchAggrQuery());
                            fundBatchAggrRoot.checkExist();
                            context.bindFundBatchAggrRoot(fundBatchAggrRoot);

                            EventContentPartialClearingVO bizContent = context.getBizContent();
                            Long difference = bizContent.getAmountDifference(fundBatchAggrRoot
                                    .getAmount()
                                    .getNeedClearingAmount());
                            if (difference.compareTo(0L) >= 0) {
                                eventAggrRoot.processSuccess("[入账结算批次明细推送]入账批次无需处理");
                                return;
                            }

                            Long balance = Math.abs(difference);
                            int pageSize = DEFAULT_PAGE_SIZE;
                            int processedCount = 0;
                            boolean hasMore = true;
                            String endCursor = null;
                            while (hasMore) {
                                List<FundBillAggrRoot> fundBills = fundBillDomainRepository.batchQuery(FundBillAggrQuery
                                        .builder()
                                        .fundBatchId(fundBatchAggrRoot.getId())
                                        .querySize(pageSize)
                                        .cursorField(FundPoolCursorModel.FundBillCursorFieldEnum.ID.getInnerField())
                                        .endCursor(endCursor)
                                        .sortField(FundBillSortModel.FundBillSortFieldEnum.ID.getInnerField())
                                        .isDesc(Boolean.FALSE)
                                        .build());

                                if (CollectionUtils.isEmpty(fundBills)) {
                                    hasMore = false;
                                    continue;
                                }
                                List<EventAggrRoot> events = Lists.newArrayList();
                                for (FundBillAggrRoot fundBill : fundBills) {
                                    if (balance.compareTo(0L) <= 0) {
                                        break;
                                    }
                                }


                                if (fundBills.size() < pageSize) {
                                    hasMore = false;
                                } else {
                                    endCursor = fundBills
                                            .getLast()
                                            .getIdStr();
                                }
                            }

                            log.info("[入账结算批次明细推送]>>>>>>处理完成，总处理资金池数量: {}", processedCount);

                        }

                        @Override
                        protected void postInvokeExternal(
                                FundBatchPartialClearingEventContext FundBatchPartialClearingEventContext) {
                        }

                        @Override
                        protected void onBizFailure(
                                FundBatchPartialClearingEventContext context, FundClearingBizException e) {
                            EventAggrRoot aggrRoot = context.getEventAggrRoot();
                            String result = e.getCode() + ":" + e.getMsg();
                            log.warn("[事件处理]>>>>>>事件处理业务异常, 事件ID: " + aggrRoot.getId() + ", 错误信息: "
                                    + result + ", 异常栈: ", e);
                            aggrRoot.processFailure("业务异常: " + result);
                        }

                        @Override
                        protected void onFailure(FundBatchPartialClearingEventContext context, Throwable throwable) {
                            EventAggrRoot aggrRoot = context.getEventAggrRoot();
                            log.error("[事件处理]>>>>>>事件处理未知异常, 事件ID: " + aggrRoot.getId() + ", 异常栈: ",
                                    throwable);
                            aggrRoot.processFailure("未知异常");
                        }

                        @Override
                        protected void doFinally(FundBatchPartialClearingEventContext context) {
                            secureUpdateEvent(context);
                        }
                    });
        }
    }
}
