package com.shouqianba.trade.fund.clearing.application.adapter.rest;

import com.shouqianba.trade.fund.clearing.application.adapter.rest.request.FundChannelNotifyRequest;
import com.shouqianba.trade.fund.clearing.application.biz.context.impl.notify.FundNotifyContext;
import com.shouqianba.trade.fund.clearing.application.biz.impl.notify.FundNotifyBiz;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Objects;

/**
 * <AUTHOR> Date: 2023/5/25 Time: 9:41 AM
 */
@Slf4j
@RestController
@RequestMapping("/fund-bill/notify")
public class NotifyController {

    public static final String SUCCESS = "success";
    public static final String FAILED = "failed";

    @Resource
    private FundNotifyBiz fundNotifyBiz;

    @PostMapping("/fund-notify")
    public ResponseEntity<String> handleClearingNotification(
            @Valid @RequestBody FundChannelNotifyRequest request) {

        if (Objects.isNull(request)) {
            return ResponseEntity.ok(FAILED);
        }

        try {
            FundNotifyContext context = FundNotifyContext.newInstance(request);
            fundNotifyBiz.process(context);
            return ResponseEntity.ok(SUCCESS);
        } catch (Exception e) {
            log.error("[资金通道通知]>>>>>>处理异常", e);
            return ResponseEntity.ok(FAILED);
        }
    }

}
