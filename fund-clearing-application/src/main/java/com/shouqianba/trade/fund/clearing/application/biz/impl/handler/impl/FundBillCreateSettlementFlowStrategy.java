package com.shouqianba.trade.fund.clearing.application.biz.impl.handler.impl;

import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.AbstractBehaviorTreeStrategy;
import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.BaseEventStrategy;
import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.context.impl.FundBillCreateSettlementFlowEventContext;
import com.shouqianba.trade.fund.clearing.common.exception.FundClearingBizException;
import com.shouqianba.trade.fund.clearing.common.template.ExternalInvokeWithTransactionTemplate;
import com.shouqianba.trade.fund.clearing.common.template.InvokeProcessor;
import com.shouqianba.trade.fund.clearing.domain.aggregate.configfundcollectrule.ConfigFundCollectRuleDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.EventAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.enums.EventTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.FundBatchDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.FundBillDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.FundBillAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.enums.FundBillStatusEnum;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.brandbusiness.BrandBusinessClient;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.brandbusiness.model.res.BrandDetailInfoGetResult;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.settle.SettleClient;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.settle.model.res.FundClearingSettlementFlowCreateResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR> Date: 2025/04/22 Time: 02:59AM
 */
@Slf4j
@Component
public class FundBillCreateSettlementFlowStrategy extends AbstractBehaviorTreeStrategy<FundBillCreateSettlementFlowEventContext> {

    private static final EventTypeEnum EVENT_TYPE = EventTypeEnum.CREATE_SETTLEMENT_FLOW;

    @Resource
    private FundBillDomainRepository fundBillDomainRepository;
    @Resource
    private ConfigFundCollectRuleDomainRepository ruleDomainRepository;
    @Resource
    private FundBatchDomainRepository fundBatchDomainRepository;
    @Resource
    private SettleClient settleClient;
    @Resource
    private BrandBusinessClient brandBusinessClient;

    @Override
    protected Pair<EventTypeEnum, BaseEventStrategy<FundBillCreateSettlementFlowEventContext>> register() {
        return Pair.of(EVENT_TYPE, this);
    }

    @Override
    protected IndependentEventStrategyRunnable getIndependentEventStrategyRunnable(
            FundBillCreateSettlementFlowEventContext context) {
        return new FundBillEntryRunnable(context);
    }

    public class FundBillEntryRunnable extends IndependentEventStrategyRunnable {
        private final FundBillCreateSettlementFlowEventContext context;

        public FundBillEntryRunnable(FundBillCreateSettlementFlowEventContext context) {
            this.context = context;
        }

        @Override
        public void run() {
            InvokeProcessor.processExternalInvokeWithTransaction(context,
                    new ExternalInvokeWithTransactionTemplate<>() {

                        @Override
                        protected boolean preInvokeExternal(FundBillCreateSettlementFlowEventContext context) {
                            return Boolean.TRUE;
                        }

                        @Override
                        protected void invokeExternal(FundBillCreateSettlementFlowEventContext context) {
                            EventAggrRoot eventAggrRoot = context.getEventAggrRoot();

                            // 1. 查询资金账单
                            FundBillAggrRoot fundBillAggrRoot =
                                    fundBillDomainRepository.query(context.genFundBillAggrQuery());
                            fundBillAggrRoot.checkExist();
                            context.bindFundBillAggrRoot(fundBillAggrRoot);

                            BrandDetailInfoGetResult brandDetailInfoGetResult =
                                    brandBusinessClient.getBrandDetailInfo(
                                            context.genBrandDetailInfoGetRequest());
                            brandDetailInfoGetResult.checkExist();
                            context.bindBrandDetailInfoGetResult(brandDetailInfoGetResult);

                            FundClearingSettlementFlowCreateResult settlementFlow = settleClient.createSettlementFlow(
                                    context.genFundClearingSettlementFlowCreateRequest());
                            if (settlementFlow.isFailed()) {
                                eventAggrRoot.processFailure("创建结算流水失败");
                                return;
                            }
                            fundBillAggrRoot.updateStatus(FundBillStatusEnum.ENTRY_SETTLED);
                            fundBillDomainRepository.save(fundBillAggrRoot);
                            eventAggrRoot.processSuccess("创建结算流水成功");
                        }

                        @Override
                        protected void postInvokeExternal(
                                FundBillCreateSettlementFlowEventContext FundBillCreateSettlementFlowEventContext) {
                        }

                        @Override
                        protected void onBizFailure(
                                FundBillCreateSettlementFlowEventContext context, FundClearingBizException e) {
                            EventAggrRoot aggrRoot = context.getEventAggrRoot();
                            String result = e.getCode() + ":" + e.getMsg();
                            log.warn("[事件处理]>>>>>>事件处理业务异常, 事件ID: " + aggrRoot.getId() + ", 错误信息: "
                                    + result + ", 异常栈: ", e);
                            aggrRoot.processFailure("业务异常: " + result);
                        }

                        @Override
                        protected void onFailure(
                                FundBillCreateSettlementFlowEventContext context, Throwable throwable) {
                            EventAggrRoot aggrRoot = context.getEventAggrRoot();
                            log.error("[事件处理]>>>>>>事件处理未知异常, 事件ID: " + aggrRoot.getId() + ", 异常栈: ",
                                    throwable);
                            aggrRoot.processFailure("未知异常");
                        }

                        @Override
                        protected void doFinally(FundBillCreateSettlementFlowEventContext context) {
                            secureUpdateEvent(context);
                        }
                    });
        }
    }
}
