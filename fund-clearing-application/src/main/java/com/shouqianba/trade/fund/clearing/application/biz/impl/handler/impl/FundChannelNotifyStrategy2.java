package com.shouqianba.trade.fund.clearing.application.biz.impl.handler.impl;

import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.AbstractBehaviorTreeStrategy;
import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.BaseEventStrategy;
import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.context.impl.FundBillEntryEventContext2;
import com.shouqianba.trade.fund.clearing.common.exception.FundClearingBizException;
import com.shouqianba.trade.fund.clearing.common.template.ExternalInvokeWithTransactionTemplate;
import com.shouqianba.trade.fund.clearing.common.template.InvokeProcessor;
import com.shouqianba.trade.fund.clearing.domain.aggregate.configfundcollectrule.ConfigFundCollectRuleDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.configfundcollectrule.model.ConfigFundCollectRuleAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.EventAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.enums.EventTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.vo.EventContentBillEntryVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.vo.EventContentVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.FundBillDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.FundBillAggrRoot;
import com.wosai.general.ds.bt.Node;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR> Date: 2025/04/22 Time: 02:59AM
 */
@Slf4j
@Component
public class FundBillEntryStrategy2 extends AbstractBehaviorTreeStrategy<FundBillEntryEventContext2> {

    private static final EventTypeEnum EVENT_TYPE = EventTypeEnum.FUND_CHANNEL_NOTIFY;

    @Resource
    private FundBillDomainRepository fundBillDomainRepository;
    @Resource
    private ConfigFundCollectRuleDomainRepository ruleDomainRepository;

    @Override
    protected Pair<EventTypeEnum, BaseEventStrategy<FundBillEntryEventContext2>> register() {
        return Pair.of(EVENT_TYPE, this);
    }

    @Override
    protected IndependentEventStrategyRunnable getIndependentEventStrategyRunnable(FundBillEntryEventContext2 context) {
        return new FundBillEntryRunnable(context);
    }

    public class FundBillEntryRunnable extends IndependentEventStrategyRunnable {
        private final FundBillEntryEventContext2 context;

        public FundBillEntryRunnable(FundBillEntryEventContext2 context) {
            this.context = context;
        }

        @Override
        public void run() {
            InvokeProcessor.processExternalInvokeWithTransaction(context,
                    new ExternalInvokeWithTransactionTemplate<>() {

                        @Override
                        protected boolean preInvokeExternal(FundBillEntryEventContext2 context) {
                            return Boolean.TRUE;
                        }

                        @Override
                        protected void invokeExternal(FundBillEntryEventContext2 context) {

                            // 1. 查询资金账单
                            FundBillAggrRoot fundBillAggrRoot =
                                    fundBillDomainRepository.query(context.genFundBillAggrQuery());
                            fundBillAggrRoot.checkExist();
                            context.bindFundBillAggrRoot(fundBillAggrRoot);

                            ConfigFundCollectRuleAggrRoot configFundCollectRuleAggrRoot =
                                    ruleDomainRepository.query(context.genConfigFundCollectRuleQuery());
                            configFundCollectRuleAggrRoot.checkExist();
                            context.bindConfigFundCollectRuleAggrRoot(configFundCollectRuleAggrRoot);

                            // 绑定行为树ID和黑板ID
                            context.bindBlackboardId(context
                                    .getBizContent()
                                    .getBlackboardId());
                            context.bindBehaviorTreeId(fundBillAggrRoot.getBehaviorTreeId());

                            // 3. 执行行为树
                            Node.NodeStatus status = executeBehaviorTreeAndProcessEventResult(context);

                            // 4. 如果非终态更新资金黑板
                            if (!status.isFinalStatus()) {
                                // 4. 更新资金账单
                                EventAggrRoot eventAggrRoot = context.getEventAggrRoot();
                                eventAggrRoot.updateContent(EventContentVO
                                        .builder()
                                        .bizContent(EventContentBillEntryVO
                                                .builder()
                                                .blackboardId(context.getBlackboardId())
                                                .build()
                                                .toJsonString())
                                        .build());
                            }

                        }

                        @Override
                        protected void postInvokeExternal(FundBillEntryEventContext2 FundBillEntryEventContext2) {
                        }

                        @Override
                        protected void onBizFailure(FundBillEntryEventContext2 context, FundClearingBizException e) {
                            EventAggrRoot aggrRoot = context.getEventAggrRoot();
                            String result = e.getCode() + ":" + e.getMsg();
                            log.warn("[事件处理]>>>>>>事件处理业务异常, 事件ID: " + aggrRoot.getId() + ", 错误信息: "
                                    + result + ", 异常栈: ", e);
                            aggrRoot.processFailure("业务异常: " + result);
                        }

                        @Override
                        protected void onFailure(FundBillEntryEventContext2 context, Throwable throwable) {
                            EventAggrRoot aggrRoot = context.getEventAggrRoot();
                            log.error("[事件处理]>>>>>>事件处理未知异常, 事件ID: " + aggrRoot.getId() + ", 异常栈: ",
                                    throwable);
                            aggrRoot.processFailure("未知异常");
                        }

                        @Override
                        protected void doFinally(FundBillEntryEventContext2 context) {
                            secureUpdateEvent(context);
                        }
                    });
        }
    }
}
