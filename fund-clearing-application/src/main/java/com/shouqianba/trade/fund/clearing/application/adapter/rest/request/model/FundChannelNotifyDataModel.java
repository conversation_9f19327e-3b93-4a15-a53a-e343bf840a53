package com.shouqianba.trade.fund.clearing.application.adapter.rest.request.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class FundChannelNotifyDataModel {

    @JsonProperty("brand_id")
    private String brandId;

    @JsonProperty("action_id")
    private String actionId;

    @JsonProperty("settlement_channel")
    private Integer settlementChannel;

    @JsonProperty("amount")
    private Long amount;

    @JsonProperty("entry_time")
    private Long entryTime;

}