package com.shouqianba.trade.fund.clearing.application.biz.context.impl.fundbill;

import com.shouqianba.trade.fund.clearing.api.request.FundBillCreateRequest;
import com.shouqianba.trade.fund.clearing.api.request.model.FundClearingBillAccountModel;
import com.shouqianba.trade.fund.clearing.api.request.model.FundClearingBillAmountModel;
import com.shouqianba.trade.fund.clearing.api.request.model.FundClearingBillBizInfoModel;
import com.shouqianba.trade.fund.clearing.api.request.model.FundClearingBillTradeInfoModel;
import com.shouqianba.trade.fund.clearing.api.result.FundBillCreateResult;
import com.shouqianba.trade.fund.clearing.application.biz.context.BaseContext;
import com.shouqianba.trade.fund.clearing.domain.aggregate.configfundcollectrulebelonging.model.enums.ConfigFundEntryTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.EventAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.EventAggrRootFactory;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.enums.EventTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.query.EventAggrQuery;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.vo.EventContentBillEntryVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.vo.EventContentVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.vo.EventExtVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.FundBillAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.FundBillAggrRootFactory;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.enums.FundBillStatusEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.enums.FundBillTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.query.FundBillAggrQuery;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.vo.FundBillAmountVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.vo.FundBillBizDomainVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.vo.FundBillExtVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.vo.FundBillTradeDomainVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.sharingrule.model.enums.AccountTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.enums.BillSourceEnum;
import com.shouqianba.trade.fund.clearing.domain.enums.PaywayEnum;
import com.shouqianba.trade.fund.clearing.domain.model.ConfigFundCollectRuleConfigVO;
import com.shouqianba.trade.fund.clearing.domain.model.PayeeInfoVO;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.brandbusiness.model.res.BrandDetailInfoGetResult;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.brandbusiness.model.res.BrandMerchantInfoQueryResult;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.coreb.model.res.StoreInfoQueryResult;
import com.shouqianba.trade.fund.clearing.infrastructure.support.DefaultSerialGenerator;
import com.shouqianba.trade.fund.clearing.infrastructure.support.model.resp.ConfigFundCollectRuleConfigResultModel;
import lombok.Getter;

import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR> Date: 2025/3/10 Time: 10:20 AM
 */
@Getter
public class FundBillCreateContext extends BaseContext {

    private final FundBillCreateRequest request;

    private BrandMerchantInfoQueryResult brandMerchantInfoQueryResult;
    private StoreInfoQueryResult storeInfoQueryResult;
    private ConfigFundCollectRuleConfigResultModel configFundCollectRuleConfigResultModel;
    private FundBillAggrRoot fundBillAggrRoot;
    private BrandDetailInfoGetResult brandDetailInfoGetResult;

    private FundBillCreateContext(FundBillCreateRequest request) {
        this.request = request;
    }

    public static FundBillCreateContext newInstance(FundBillCreateRequest request) {
        return new FundBillCreateContext(request);
    }

    public void bindBrandDetailInfoGetResult(BrandDetailInfoGetResult brandDetailInfo) {
        this.brandDetailInfoGetResult = brandDetailInfo;
    }

    public void bindBrandMerchantInfoQueryResult(BrandMerchantInfoQueryResult brandMerchantInfoQueryResult) {
        this.brandMerchantInfoQueryResult = brandMerchantInfoQueryResult;
    }

    public void bindStoreInfoQueryResult(StoreInfoQueryResult storeInfoQueryResult) {
        this.storeInfoQueryResult = storeInfoQueryResult;
    }

    public void bindConfigFundCollectRuleConfigResultModel(
            ConfigFundCollectRuleConfigResultModel configFundCollectRuleConfigResultModel) {
        this.configFundCollectRuleConfigResultModel = configFundCollectRuleConfigResultModel;
    }

    public void bindFundBillAggrRoot(FundBillAggrRoot fundBillAggrRoot) {
        this.fundBillAggrRoot = fundBillAggrRoot;
    }

    public FundBillAggrQuery genFundBillQuery() {
        FundClearingBillAccountModel payeeInfo = getRequest().getPayeeInfo();
        return FundBillAggrQuery
                .builder()
                .payeeType(payeeInfo.getType())
                .billSource(request.getBillSource())
                .transSn(request.getTransSn())
                .build();
    }

    public boolean isNotNeedTriggerFundClearingEvent() {
        return fundBillAggrRoot.isInConfirm();
    }


    public EventAggrQuery genBillEntryToBatchEventQuery() {
        return EventAggrQuery
                .builder()
                .associatedSn(fundBillAggrRoot.getIdStr())
                .type(EventTypeEnum.BILL_ENTRY_TO_BATCH)
                .build();
    }

    public EventAggrRoot genBillEntryToBatchEvent() {
        Long eventId = DefaultSerialGenerator
                .getInstance()
                .genEventId();
        return EventAggrRootFactory
                .builder()
                .coreBuilder()
                .id(eventId)
                .type(EventTypeEnum.BILL_ENTRY_TO_BATCH)
                .associatedSn(fundBillAggrRoot.getIdStr())
                .optionalBuilder()
                .content(EventContentVO
                        .builder()
                        .bizContent(EventContentBillEntryVO
                                .builder()
                                .build()
                                .toJsonString())
                        .build())
                .ext(EventExtVO
                        .builder()
                        .mqKey(fundBillAggrRoot.getMerchantSn())
                        .build())
                .build();
    }

    public FundBillStatusEnum genFundBillStatus() {
        ConfigFundEntryTypeEnum entryType = configFundCollectRuleConfigResultModel
                .getConfig()
                .getEntryType();
        return Optional
                .of(entryType)
                .filter(type -> Objects.equals(type, ConfigFundEntryTypeEnum.REAL_TIME))
                .map(type -> FundBillStatusEnum.IN_ENTRY)
                .orElse(FundBillStatusEnum.IN_CONFIRMATION);
    }

    public FundBillAggrRoot genFundBillAggrRoot() {
        FundClearingBillAccountModel payeeInfo = request.getPayeeInfo();
        FundClearingBillTradeInfoModel trade = request.getTrade();
        FundClearingBillAmountModel amount = request.getAmount();
        FundBillTypeEnum type = FundBillTypeEnum.ofCode(request.getType());
        ConfigFundCollectRuleConfigVO config = configFundCollectRuleConfigResultModel.getConfig();
        return FundBillAggrRootFactory
                .builder()
                .coreBuilder()
                .id(DefaultSerialGenerator
                        .getInstance()
                        .genFundBillId())
                .acquiringDate(request.parseAcquiringDate())
                .billDate(request.parseBilDate())
                .payeeInfo(PayeeInfoVO
                        .builder()
                        .brandSn(brandMerchantInfoQueryResult.getBrandSn())
                        .brandId(brandMerchantInfoQueryResult.getBrandId())
                        .fundMerchantId(brandMerchantInfoQueryResult.getMerchantId())
                        .fundMerchantSn(brandMerchantInfoQueryResult.getMerchantSn())
                        .sqbMerchantId(Optional
                                .ofNullable(storeInfoQueryResult)
                                .map(StoreInfoQueryResult::getMerchantId)
                                .orElse(null))
                        .sqbMerchantSn(Optional
                                .ofNullable(storeInfoQueryResult)
                                .map(StoreInfoQueryResult::getMerchantSn)
                                .orElse(null))
                        .storeSn(Optional
                                .ofNullable(storeInfoQueryResult)
                                .map(StoreInfoQueryResult::getStoreSn)
                                .orElse(null))
                        .storeId(Optional
                                .ofNullable(storeInfoQueryResult)
                                .map(StoreInfoQueryResult::getStoreId)
                                .orElse(null))
                        .build())
                .brandSn(payeeInfo.getBrandSn())
                .merchantSn(payeeInfo.getMerchantSn())
                .storeSn(payeeInfo.getStoreSn())
                .payeeType(AccountTypeEnum.ofCode(payeeInfo.getType()))
                .billSource(BillSourceEnum.ofCode(request.getBillSource()))
                .type(type)
                .status(genFundBillStatus())
                .amount(FundBillAmountVO
                        .builder()
                        .originAmount(amount.getOriAmount())
                        .fee(amount.getFee())
                        .build())
                .transSn(request.getTransSn())
                .tradeDomain(FundBillTradeDomainVO
                        .builder()
                        .acquiringCompany(trade.getAcquiringCompany())
                        .payWay(PaywayEnum.ofCode(trade.getPayWay()))
                        .tradeTime(trade.getTradeTime())
                        .settleBeginTime(trade.getBeginDate())
                        .settleEndTime(trade.getEndDate())
                        .build())
                .optionalBuilder()
                .orderSn(request.getOrderSn())
                .bizDomain(FundBillBizDomainVO
                        .builder()
                        .channelBillInfo(Optional
                                .ofNullable(request.getBiz())
                                .map(FundClearingBillBizInfoModel::getChannelBillInfo)
                                .orElse(null))
                        .fundCollectRuleId(configFundCollectRuleConfigResultModel.getRuleId())
                        .fundCollectRuleBelongId(configFundCollectRuleConfigResultModel.getBelongId())
                        .entryBehaviorTreeId(config.getBillEntryBehaviorTreeId())
                        .build())
                .ext(FundBillExtVO
                        .builder()
                        .build())
                .build();
    }

    public FundBillCreateResult genResult() {
        return new FundBillCreateResult().setBillId(fundBillAggrRoot.getIdStr());
    }
}


