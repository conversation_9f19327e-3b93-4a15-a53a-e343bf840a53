package com.shouqianba.trade.fund.clearing.application.biz.impl.handler.context.impl;

import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.context.AbstractBehaviorTreeContext;
import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.context.EventStrategyContext;
import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.holder.EventHandlerContext;
import com.shouqianba.trade.fund.clearing.common.util.JsonUtils;
import com.shouqianba.trade.fund.clearing.domain.aggregate.configblackboard.model.ConfigBlackboardAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.configblackboard.model.ConfigBlackboardAggrRootFactory;
import com.shouqianba.trade.fund.clearing.domain.aggregate.configblackboard.model.vo.ConfigBlackboardExtVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.configblackboard.model.vo.ConfigBlackboardValueDomainVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.configfundcollectrule.model.ConfigFundCollectRuleAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.configfundcollectrule.model.query.ConfigFundCollectRuleAggrQuery;
import com.shouqianba.trade.fund.clearing.domain.aggregate.configfundcollectrulebelonging.model.query.ConfigFundCollectRuleBelongAggrQuery;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.EventAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.EventAggrRootFactory;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.enums.EventTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.vo.EventContentClearingNotifyVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.vo.EventContentFundBatchBillEntryToPoolVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.vo.EventContentVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.vo.EventExtVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.model.FundBatchAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.model.query.FundBatchAggrQuery;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.FundBillAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.query.FundBillAggrQuery;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.FundPoolAggrRoot;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.brandbusiness.model.req.BrandDetailInfoGetRequest;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.brandbusiness.model.res.BrandDetailInfoGetResult;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.settle.model.req.FundClearingSettlementFlowFinishCreateRequest;
import com.shouqianba.trade.fund.clearing.infrastructure.support.DefaultSerialGenerator;
import com.shouqianba.trade.fund.clearing.infrastructure.support.bt.blackbord.ClearingCustomBlackboard;
import lombok.Getter;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR> Date: 2025/04/22 Time: 02:59AM
 */
@Getter
@Component
public class ClearingNotifyEventContext extends AbstractBehaviorTreeContext<EventContentClearingNotifyVO> {
    private static final EventTypeEnum EVENT_TYPE = EventTypeEnum.CREATE_SETTLEMENT_FLOW_MONITOR;
    public static final byte FLOW_TYPE = (byte) 1;
    public static final byte FROM_TYPE = (byte) 1;


    private EventContentClearingNotifyVO eventContent;
    private FundPoolAggrRoot fundPoolAggrRoot;
    private FundBillAggrRoot fundBillAggrRoot;
    private FundBatchAggrRoot fundBatchAggrRoot;
    private BrandDetailInfoGetResult brandDetailInfoGetResult;
    private ConfigFundCollectRuleAggrRoot configFundCollectRuleAggrRoot;

    static {
        registerContext(EVENT_TYPE, new ClearingNotifyEventContext());
    }

    private ClearingNotifyEventContext() {
        super();
    }

    private ClearingNotifyEventContext(EventAggrRoot eventAggrRoot) {
        super(eventAggrRoot);
    }

    @Override
    public EventStrategyContext<EventContentClearingNotifyVO> rebuildContext(EventHandlerContext context) {
        return new ClearingNotifyEventContext(context.getEventAggrRoot());
    }

    @Override
    public EventContentClearingNotifyVO getBizContent() {
        if (Objects.isNull(eventContent)) {
            eventContent = JsonUtils.parseObject(getEventAggrRoot()
                            .getContent()
                            .getBizContent()
                    , EventContentClearingNotifyVO.class);
        }
        return eventContent;
    }

    public FundBillAggrQuery genFundBillAggrQuery() {
        return FundBillAggrQuery
                .builder()
                .fundBatchId(getEventAggrRoot().getAssociatedSnNum())
                .build();
    }

    public ConfigFundCollectRuleBelongAggrQuery genConfigFundCollectRuleBelongQuery() {
        return ConfigFundCollectRuleBelongAggrQuery
                .builder()
                .id(fundBillAggrRoot.getConfigFundCollectRuleBelongId())
                .build();
    }

    public void bindFundBillAggrRoot(FundBillAggrRoot fundBillAggrRoot) {
        this.fundBillAggrRoot = fundBillAggrRoot;
    }

    public void bindFundPoolAggrRoot(FundPoolAggrRoot first) {
        this.fundPoolAggrRoot = first;
    }

    public ConfigBlackboardAggrRoot genConfigBlackboardAggrRoot() {
        return ConfigBlackboardAggrRootFactory
                .builder()
                .coreBuilder()
                .id(DefaultSerialGenerator
                        .getInstance()
                        .genConfigBlackboardId())
                .valueDomain(ConfigBlackboardValueDomainVO
                        .builder()
                        .valueData(blackboard.toJsonNode())
                        .build())
                .optionalBuilder()
                .ext(ConfigBlackboardExtVO
                        .builder()
                        .remark("入账黑板")
                        .build())
                .build();
    }

    @Override
    public Long getBehaviorTreeId() {
        return fundBillAggrRoot.getBehaviorTreeId();
    }

    @Override
    public ClearingCustomBlackboard genClearingCustomBlackboard() {
        ClearingCustomBlackboard blackboard = new ClearingCustomBlackboard.ClearingCustomBlackboardBuilder().build();
        blackboard.setFundBillAggrRootId(fundBillAggrRoot.getIdStr());
        blackboard.setRuleBelongAggrRootId(fundBillAggrRoot.getConfigFundCollectRuleBelongIdStr());
        return blackboard;
    }

    public ConfigFundCollectRuleAggrQuery genConfigFundCollectRuleQuery() {
        return ConfigFundCollectRuleAggrQuery
                .builder()
                .id(getFundBillAggrRoot().getCollectionRuleId())
                .build();
    }

    public void bindConfigFundCollectRuleAggrRoot(ConfigFundCollectRuleAggrRoot configFundCollectRuleAggrRoot) {
        this.configFundCollectRuleAggrRoot = configFundCollectRuleAggrRoot;
    }


    public FundBatchAggrQuery genFundBatchAggrQuery() {
        return FundBatchAggrQuery
                .builder()
                .id(getBizContent().getClientSnNum())
                .build();
    }


    public BrandDetailInfoGetRequest genBrandDetailInfoGetRequest() {
        return BrandDetailInfoGetRequest
                .builder()
                .brandId(fundBillAggrRoot
                        .getPayeeInfo()
                        .getBrandId())
                .build();
    }

    public void bindBrandDetailInfoGetResult(BrandDetailInfoGetResult brandDetailInfoGetResult) {
        this.brandDetailInfoGetResult = brandDetailInfoGetResult;
    }

    public void bindFundBatchAggrRoot(FundBatchAggrRoot fundBatchAggrRoot) {
        this.fundBatchAggrRoot = fundBatchAggrRoot;
    }

    public FundClearingSettlementFlowFinishCreateRequest genFundClearingSettlementFlowFinishCreateRequest() {
        return FundClearingSettlementFlowFinishCreateRequest
                .builder()
                .batchId(fundBatchAggrRoot.getSettleBatchId())
                .build();
    }

    public boolean isClearingSuccess() {
        return getBizContent().isClearingSuccess();
    }

    public EventAggrRoot genFundBatchBillEntryToPoolEventAggrRoot() {
        return EventAggrRootFactory
                .builder()
                .coreBuilder()
                .id(DefaultSerialGenerator
                        .getInstance()
                        .genEventId())
                .type(EventTypeEnum.FUND_BATCH_BILL_ENTRY_TO_POOL)
                .associatedSn(fundBatchAggrRoot.getIdStr())
                .optionalBuilder()
                .content(EventContentVO
                        .builder()
                        .bizContent(EventContentFundBatchBillEntryToPoolVO
                                .builder()
                                .build()
                                .toJsonString())
                        .build())
                .ext(EventExtVO
                        .builder()
                        .mqKey(fundBatchAggrRoot.getMerchantSn())
                        .build())
                .build();
    }

    public EventAggrRoot genFundBatchBillEntryToPoolMonitorEventAggrRoot() {
        return EventAggrRootFactory
                .builder()
                .coreBuilder()
                .id(DefaultSerialGenerator
                        .getInstance()
                        .genEventId())
                .type(EventTypeEnum.FUND_BATCH_BILL_ENTRY_TO_POOL_MONITOR)
                .associatedSn(fundBatchAggrRoot.getIdStr())
                .optionalBuilder()
                .content(EventContentVO
                        .builder()
                        .bizContent(EventContentFundBatchBillEntryToPoolVO
                                .builder()
                                .build()
                                .toJsonString())
                        .build())
                .ext(EventExtVO
                        .builder()
                        .mqKey(fundBatchAggrRoot.getMerchantSn())
                        .build())
                .build();
    }
}
