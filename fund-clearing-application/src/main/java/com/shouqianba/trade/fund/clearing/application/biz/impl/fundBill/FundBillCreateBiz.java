package com.shouqianba.trade.fund.clearing.application.biz.impl.fundBill;


import com.shouqianba.trade.fund.clearing.api.request.model.FundClearingBillAccountModel;
import com.shouqianba.trade.fund.clearing.application.biz.BaseBiz;
import com.shouqianba.trade.fund.clearing.application.biz.context.impl.fundbill.FundBillCreateContext;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.EventDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.EventAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.FundBillDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.FundBillAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.sharingrule.model.enums.AccountTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.enums.BillSourceEnum;
import com.shouqianba.trade.fund.clearing.infrastructure.support.FundClearingRuleSupportFacade;
import com.shouqianba.trade.fund.clearing.infrastructure.support.model.ConfigFundCollectRuleConfigGetModel;
import com.shouqianba.trade.fund.clearing.infrastructure.support.model.resp.ConfigFundCollectRuleConfigResultModel;
import com.shouqianba.trade.fund.clearing.infrastructure.support.service.ClearingAccountSupportService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


/**
 * <AUTHOR> Date: 2025/4/21 Time: 10:20 AM
 */
@Service
public class FundBillCreateBiz extends BaseBiz<FundBillCreateContext> {

    @Resource
    private FundBillDomainRepository fundBillDomainRepository;
    @Resource
    private EventDomainRepository eventDomainRepository;
    @Resource
    private ClearingAccountSupportService clearingAccountSupportService;
    @Resource
    private FundClearingRuleSupportFacade fundClearingRuleSupportFacade;

    @Override
    protected void doBiz(FundBillCreateContext context) {
        // 1. 处理收款方信息
        handlePayeeInfo(context);

        // 2. 处理资金归集规则配置
        handleFundCollectRuleConfig(context);

        // 3. 保存资金账单
        saveFundBillAggrRoot(context);

        // 4. 触发入账事件
        triggerBillEntryToBatchEvent(context);
    }

    private void handlePayeeInfo(FundBillCreateContext context) {
        FundClearingBillAccountModel payeeInfo = context
                .getRequest()
                .getPayeeInfo();

        ClearingAccountSupportService.AccountInfoResult accountInfoResult =
                clearingAccountSupportService.processAccountInfo(payeeInfo);

        // 绑定查询结果到上下文
        if (accountInfoResult.getBrandDetailInfo() != null) {
            context.bindBrandDetailInfoGetResult(accountInfoResult.getBrandDetailInfo());
        }

        if (accountInfoResult.getMerchantInfo() != null) {
            context.bindBrandMerchantInfoQueryResult(accountInfoResult.getMerchantInfo());
        }

        if (accountInfoResult.getStoreInfo() != null) {
            context.bindStoreInfoQueryResult(accountInfoResult.getStoreInfo());
        }
    }

    private void handleFundCollectRuleConfig(FundBillCreateContext context) {
        FundClearingBillAccountModel payeeInfo = context
                .getRequest()
                .getPayeeInfo();

        // 构建请求对象
        ConfigFundCollectRuleConfigGetModel req = ConfigFundCollectRuleConfigGetModel
                .builder()
                .billSource(BillSourceEnum.ofCode(context
                        .getRequest()
                        .getBillSource()))
                .accountType(AccountTypeEnum.ofCode(payeeInfo.getType()))
                .brandSn(payeeInfo.getBrandSn())
                .merchantSn(payeeInfo.getMerchantSn())
                .storeSn(payeeInfo.getStoreSn())
                .build();

        // 调用服务获取配置
        ConfigFundCollectRuleConfigResultModel ruleConfigModel =
                fundClearingRuleSupportFacade.getConfigFundCollectRuleConfig(req);

        // 检查配置是否存在
        ruleConfigModel
                .getConfig()
                .checkExist();

        // 绑定配置到上下文
        context.bindConfigFundCollectRuleConfigResultModel(ruleConfigModel);
    }


    private void saveFundBillAggrRoot(FundBillCreateContext context) {
        try {
            List<FundBillAggrRoot> fundBillAggrRoots = fundBillDomainRepository.batchQuery(context.genFundBillQuery());
            if (CollectionUtils.isNotEmpty(fundBillAggrRoots)) {
                context.bindFundBillAggrRoot(fundBillAggrRoots.getFirst());
                return;
            }
            FundBillAggrRoot fundBillAggrRoot = context.genFundBillAggrRoot();
            fundBillDomainRepository.save(fundBillAggrRoot);
            context.bindFundBillAggrRoot(fundBillAggrRoot);
        } catch (DuplicateKeyException ignore) {
            List<FundBillAggrRoot> fundBillAggrRoots = fundBillDomainRepository.batchQuery(context.genFundBillQuery());
            context.bindFundBillAggrRoot(fundBillAggrRoots.getFirst());
        }
    }

    private void triggerBillEntryToBatchEvent(FundBillCreateContext context) {
        if (context.isNotNeedTriggerFundClearingEvent()) {
            return;
        }
        List<EventAggrRoot> eventAggrRoots = eventDomainRepository.batchQuery(context.genBillEntryToBatchEventQuery());
        if (CollectionUtils.isNotEmpty(eventAggrRoots)) {
            return;
        }
        eventDomainRepository.save(context.genBillEntryToBatchEvent());
    }
}

