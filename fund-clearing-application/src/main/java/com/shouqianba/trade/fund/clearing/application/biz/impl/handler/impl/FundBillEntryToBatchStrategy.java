package com.shouqianba.trade.fund.clearing.application.biz.impl.handler.impl;

import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.AbstractBehaviorTreeStrategy;
import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.BaseEventStrategy;
import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.context.impl.FundBillEntryEventContext;
import com.shouqianba.trade.fund.clearing.common.exception.FundClearingBizException;
import com.shouqianba.trade.fund.clearing.common.exception.enums.FundClearingRespCodeEnum;
import com.shouqianba.trade.fund.clearing.common.template.ExternalInvokeWithTransactionTemplate;
import com.shouqianba.trade.fund.clearing.common.template.InvokeProcessor;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.EventAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.enums.EventTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.FundBatchDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.model.FundBatchAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.model.FundBatchAggrRootFactory;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.model.enums.FundBatchStatusEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.model.query.FundBatchAggrQuery;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.model.vo.FundBatchAmountVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.FundBillDomainRepository;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.FundBillAggrRoot;
import com.shouqianba.trade.fund.clearing.infrastructure.support.DefaultSerialGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> Date: 2025/04/22 Time: 02:59AM
 */
@Slf4j
@Component
public class FundBillEntryToBatchStrategy extends AbstractBehaviorTreeStrategy<FundBillEntryEventContext> {

    private static final EventTypeEnum EVENT_TYPE = EventTypeEnum.BILL_ENTRY_TO_BATCH;

    @Resource
    private FundBillDomainRepository fundBillDomainRepository;
    @Resource
    private FundBatchDomainRepository fundBatchDomainRepository;

    @Override
    protected Pair<EventTypeEnum, BaseEventStrategy<FundBillEntryEventContext>> register() {
        return Pair.of(EVENT_TYPE, this);
    }

    @Override
    protected IndependentEventStrategyRunnable getIndependentEventStrategyRunnable(FundBillEntryEventContext context) {
        return new FundBillEntryRunnable(context);
    }

    public class FundBillEntryRunnable extends IndependentEventStrategyRunnable {
        private final FundBillEntryEventContext context;

        public FundBillEntryRunnable(FundBillEntryEventContext context) {
            this.context = context;
        }

        @Override
        public void run() {
            InvokeProcessor.processExternalInvokeWithTransaction(context,
                    new ExternalInvokeWithTransactionTemplate<>() {

                        @Override
                        protected boolean preInvokeExternal(FundBillEntryEventContext context) {
                            return Boolean.TRUE;
                        }

                        @Override
                        protected void invokeExternal(FundBillEntryEventContext context) {

                            // 1. 查询资金账单
                            FundBillAggrRoot fundBillAggrRoot =
                                    fundBillDomainRepository.query(context.genFundBillAggrQuery());
                            fundBillAggrRoot.checkExist();
                            context.bindFundBillAggrRoot(fundBillAggrRoot);

                            FundBatchAggrRoot fundBatchAggrRoot;
                            List<FundBatchAggrRoot> fundBatchAggrRoots =
                                    fundBatchDomainRepository.batchQuery(FundBatchAggrQuery
                                            .builder()
                                            .brandSn(fundBillAggrRoot.getBrandSn())
                                            .billSource(fundBillAggrRoot
                                                    .getBillSource()
                                                    .getCode())
                                            .acquiringCompany(fundBillAggrRoot.getAcquiringCompany())
                                            .settleTime(fundBillAggrRoot.genSettleTime())
                                            .querySize(1)
                                            .build());

                            if (CollectionUtils.isEmpty(fundBatchAggrRoots)) {
                                try {
                                    fundBatchDomainRepository.save(FundBatchAggrRootFactory
                                            .builder()
                                            .coreBuilder()
                                            .id(DefaultSerialGenerator
                                                    .getInstance()
                                                    .genFundBatchId())
                                            .payeeType(fundBillAggrRoot.getPayeeType())
                                            .brandSn(fundBillAggrRoot.getBrandSn())
                                            .merchantSn(fundBillAggrRoot.getMerchantSn())
                                            .storeSn(fundBillAggrRoot.getStoreSn())
                                            .payeeInfo(fundBillAggrRoot.getPayeeInfo())
                                            .billSource(fundBillAggrRoot.getBillSource())
                                            .acquiringCompany(fundBillAggrRoot.getAcquiringCompany())
                                            .settleTime(fundBillAggrRoot.genSettleTime())
                                            .status(FundBatchStatusEnum.ENTERING)
                                            .amount(FundBatchAmountVO.newEmptyInstance())
                                            .build());
                                } catch (DuplicateKeyException ignore) {
                                }
                                fundBatchAggrRoots = fundBatchDomainRepository.batchQuery(FundBatchAggrQuery
                                        .builder()
                                        .brandSn(fundBillAggrRoot.getBrandSn())
                                        .billSource(fundBillAggrRoot
                                                .getBillSource()
                                                .getCode())
                                        .acquiringCompany(fundBillAggrRoot.getAcquiringCompany())
                                        .settleTime(fundBillAggrRoot.genSettleTime())
                                        .querySize(1)
                                        .build());
                                if (CollectionUtils.isEmpty(fundBatchAggrRoots)) {
                                    throw new FundClearingBizException(FundClearingRespCodeEnum.FUND_BATCH_NOT_EXIST);
                                }
                            }
                            fundBatchAggrRoot = fundBatchAggrRoots.getFirst();
                            fundBatchAggrRoot.updateAmount(fundBatchAggrRoot
                                    .getAmount()
                                    .add(fundBillAggrRoot.getAmount()));
                            fundBatchDomainRepository.save(fundBatchAggrRoot);

                        }

                        @Override
                        protected void postInvokeExternal(FundBillEntryEventContext FundBillEntryEventContext) {
                        }

                        @Override
                        protected void onBizFailure(FundBillEntryEventContext context, FundClearingBizException e) {
                            EventAggrRoot aggrRoot = context.getEventAggrRoot();
                            String result = e.getCode() + ":" + e.getMsg();
                            log.warn("[事件处理]>>>>>>事件处理业务异常, 事件ID: " + aggrRoot.getId() + ", 错误信息: "
                                    + result + ", 异常栈: ", e);
                            aggrRoot.processFailure("业务异常: " + result);
                        }

                        @Override
                        protected void onFailure(FundBillEntryEventContext context, Throwable throwable) {
                            EventAggrRoot aggrRoot = context.getEventAggrRoot();
                            log.error("[事件处理]>>>>>>事件处理未知异常, 事件ID: " + aggrRoot.getId() + ", 异常栈: ",
                                    throwable);
                            aggrRoot.processFailure("未知异常");
                        }

                        @Override
                        protected void doFinally(FundBillEntryEventContext context) {
                            secureUpdateEvent(context);
                        }
                    });
        }
    }
}
