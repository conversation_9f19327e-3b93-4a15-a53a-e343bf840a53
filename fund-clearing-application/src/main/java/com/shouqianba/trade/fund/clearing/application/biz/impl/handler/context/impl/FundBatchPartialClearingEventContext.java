package com.shouqianba.trade.fund.clearing.application.biz.impl.handler.context.impl;

import com.shouqianba.trade.fund.clearing.api.request.model.FundClearingBillAccountModel;
import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.context.AbstractBehaviorTreeContext;
import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.context.EventStrategyContext;
import com.shouqianba.trade.fund.clearing.application.biz.impl.handler.holder.EventHandlerContext;
import com.shouqianba.trade.fund.clearing.common.util.JsonUtils;
import com.shouqianba.trade.fund.clearing.domain.aggregate.configblackboard.model.ConfigBlackboardAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.configblackboard.model.ConfigBlackboardAggrRootFactory;
import com.shouqianba.trade.fund.clearing.domain.aggregate.configblackboard.model.vo.ConfigBlackboardExtVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.configblackboard.model.vo.ConfigBlackboardValueDomainVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.configfundcollectrule.model.ConfigFundCollectRuleAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.configfundcollectrule.model.query.ConfigFundCollectRuleAggrQuery;
import com.shouqianba.trade.fund.clearing.domain.aggregate.configfundcollectrulebelonging.model.query.ConfigFundCollectRuleBelongAggrQuery;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.EventAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.EventAggrRootFactory;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.enums.EventTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.vo.EventContentBillEntryVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.vo.EventContentCreateSettlementFlowVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.vo.EventContentVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.vo.EventExtVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.model.FundBatchAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.model.query.FundBatchAggrQuery;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.FundBillAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.query.FundBillAggrQuery;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.FundPoolAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.sharingrule.model.enums.AccountTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.sharingrule.model.enums.SettleTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.model.PayeeInfoVO;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.coreb.model.res.StoreInfoQueryResult;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.settle.model.req.FundClearingSettlementBatchCreateRequest;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.externalservice.settle.model.res.FundClearingSettlementBatchCreateResult;
import com.shouqianba.trade.fund.clearing.infrastructure.support.DefaultSerialGenerator;
import com.shouqianba.trade.fund.clearing.infrastructure.support.bt.blackbord.ClearingCustomBlackboard;
import com.shouqianba.trade.fund.clearing.infrastructure.support.service.ClearingAccountSupportService;
import lombok.Getter;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR> Date: 2025/04/22 Time: 02:59AM
 */
@Getter
@Component
public class FundBatchFullClearingEventContext extends AbstractBehaviorTreeContext<EventContentBillEntryVO> {

    private static final EventTypeEnum EVENT_TYPE = EventTypeEnum.BILL_ENTRY_TO_BATCH;
    public static final byte TYPE_ENTRY = (byte) 1;

    private EventContentBillEntryVO eventContent;
    private FundBatchAggrRoot fundBatchAggrRoot;
    private ClearingAccountSupportService.AccountInfoResult accountInfoResult;
    private FundClearingSettlementBatchCreateResult fundClearingSettlementBatchCreateResult;

    private FundPoolAggrRoot fundPoolAggrRoot;
    private FundBillAggrRoot fundBillAggrRoot;
    private ConfigFundCollectRuleAggrRoot configFundCollectRuleAggrRoot;

    static {
        registerContext(EVENT_TYPE, new FundBatchFullClearingEventContext());
    }

    private FundBatchFullClearingEventContext() {
        super();
    }

    private FundBatchFullClearingEventContext(EventAggrRoot eventAggrRoot) {
        super(eventAggrRoot);
    }

    @Override
    public EventStrategyContext<EventContentBillEntryVO> rebuildContext(EventHandlerContext context) {
        return new FundBatchFullClearingEventContext(context.getEventAggrRoot());
    }

    @Override
    public EventContentBillEntryVO getBizContent() {
        if (Objects.isNull(eventContent)) {
            eventContent = JsonUtils.parseObject(getEventAggrRoot()
                            .getContent()
                            .getBizContent()
                    , EventContentBillEntryVO.class);
        }
        return eventContent;
    }

    public FundBillAggrQuery genFundBillAggrQuery() {
        return FundBillAggrQuery
                .builder()
                .id(getEventAggrRoot().getAssociatedSnNum())
                .build();
    }

    public ConfigFundCollectRuleBelongAggrQuery genConfigFundCollectRuleBelongQuery() {
        return ConfigFundCollectRuleBelongAggrQuery
                .builder()
                .id(fundBillAggrRoot.getConfigFundCollectRuleBelongId())
                .build();
    }

    public void bindFundBillAggrRoot(FundBillAggrRoot fundBillAggrRoot) {
        this.fundBillAggrRoot = fundBillAggrRoot;
    }

    public void bindFundPoolAggrRoot(FundPoolAggrRoot first) {
        this.fundPoolAggrRoot = first;
    }

    public ConfigBlackboardAggrRoot genConfigBlackboardAggrRoot() {
        return ConfigBlackboardAggrRootFactory
                .builder()
                .coreBuilder()
                .id(DefaultSerialGenerator
                        .getInstance()
                        .genConfigBlackboardId())
                .valueDomain(ConfigBlackboardValueDomainVO
                        .builder()
                        .valueData(blackboard.toJsonNode())
                        .build())
                .optionalBuilder()
                .ext(ConfigBlackboardExtVO
                        .builder()
                        .remark("入账黑板")
                        .build())
                .build();
    }

    @Override
    public Long getBehaviorTreeId() {
        return fundBillAggrRoot.getBehaviorTreeId();
    }

    @Override
    public ClearingCustomBlackboard genClearingCustomBlackboard() {
        ClearingCustomBlackboard blackboard = new ClearingCustomBlackboard.ClearingCustomBlackboardBuilder().build();
        blackboard.setFundBillAggrRootId(fundBillAggrRoot.getIdStr());
        blackboard.setRuleBelongAggrRootId(fundBillAggrRoot.getConfigFundCollectRuleBelongIdStr());
        return blackboard;
    }

    public ConfigFundCollectRuleAggrQuery genConfigFundCollectRuleQuery() {
        return ConfigFundCollectRuleAggrQuery
                .builder()
                .id(getFundBillAggrRoot().getCollectionRuleId())
                .build();
    }

    public void bindConfigFundCollectRuleAggrRoot(ConfigFundCollectRuleAggrRoot configFundCollectRuleAggrRoot) {
        this.configFundCollectRuleAggrRoot = configFundCollectRuleAggrRoot;
    }

    public FundBatchAggrQuery genFundBatchAggrQuery() {
        return FundBatchAggrQuery
                .builder()
                .id(getEventAggrRoot().getAssociatedSnNum())
                .build();
    }

    public void bindFundBatchAggrRoot(FundBatchAggrRoot fundBatchAggrRoot) {
        this.fundBatchAggrRoot = fundBatchAggrRoot;
    }

    public FundClearingBillAccountModel genFundClearingBillAccountModel() {
        return FundClearingBillAccountModel
                .builder()
                .type(AccountTypeEnum.BRAND.getCode())
                .brandSn(getFundBatchAggrRoot().getBrandSn())
                .build();
    }

    public FundClearingSettlementBatchCreateRequest genFundClearingSettlementBatchCreateRequest() {
        FundClearingSettlementBatchCreateRequest request = FundClearingSettlementBatchCreateRequest
                .builder()
                .type(TYPE_ENTRY)
                .fromInfo(FundClearingSettlementBatchCreateRequest.AccountInfo
                        .builder()
                        .type(AccountTypeEnum.BRAND.getCode())
                        .brandSn(accountInfoResult
                                .getBrandDetailInfo()
                                .getBrandSn())
                        .merchantSn(accountInfoResult
                                .getBrandDetailInfo()
                                .getMerchantSn())
                        .storeSn(Optional
                                .ofNullable(accountInfoResult.getStoreInfo())
                                .map(
                                        StoreInfoQueryResult::getStoreSn)
                                .orElse(null))
                        .build())
                .toInfo(convertToAccountInfo(fundBatchAggrRoot.getPayeeInfo(),
                        AccountTypeEnum.MERCHANT.getCode()))
                .amount(FundClearingSettlementBatchCreateRequest.AmountInfo
                        .builder()
                        .originAmount(fundBatchAggrRoot.getTotalAmount())
                        .fee(fundBatchAggrRoot.getFeeAmount())
                        .settleAmount(fundBatchAggrRoot.getSettleAmount())
                        .build())
                .settleType(SettleTypeEnum.SETTLEMENT.getCode())
                .build();
        return request;
    }

    private FundClearingSettlementBatchCreateRequest.AccountInfo convertToAccountInfo(
            PayeeInfoVO payeeInfo, Byte accountType) {
        return FundClearingSettlementBatchCreateRequest.AccountInfo
                .builder()
                .type(accountType)
                .brandSn(Optional
                        .ofNullable(payeeInfo)
                        .map(PayeeInfoVO::getBrandSn)
                        .orElse(null))
                .merchantSn(Optional
                        .ofNullable(payeeInfo)
                        .map(PayeeInfoVO::getFundMerchantSn)
                        .orElse(null))
                .storeSn(Optional
                        .ofNullable(payeeInfo)
                        .map(PayeeInfoVO::getStoreSn)
                        .orElse(null))
                .build();
    }

    public void bindClearingAccountInfoResult(ClearingAccountSupportService.AccountInfoResult accountInfoResult) {
        this.accountInfoResult = accountInfoResult;
    }

    public void bindFundClearingSettlementBatchCreateResult(
            FundClearingSettlementBatchCreateResult fundClearingSettlementBatchCreateResult) {
        this.fundClearingSettlementBatchCreateResult = fundClearingSettlementBatchCreateResult;
    }

    public EventAggrRoot genCreateSettlementFlowEvent(FundBillAggrRoot fundBill) {
        Long eventId = DefaultSerialGenerator
                .getInstance()
                .genEventId();
        return EventAggrRootFactory
                .builder()
                .coreBuilder()
                .id(eventId)
                .type(EventTypeEnum.CREATE_SETTLEMENT_FLOW)
                .associatedSn(fundBill.getIdStr())
                .optionalBuilder()
                .content(EventContentVO
                        .builder()
                        .bizContent(EventContentCreateSettlementFlowVO
                                .builder()
                                .settleBatchId(fundClearingSettlementBatchCreateResult.getBatchId())
                                .build()
                                .toJsonString())
                        .build())
                .ext(EventExtVO
                        .builder()
                        .mqKey(fundBill.getMerchantSn())
                        .build())
                .build();
    }

    public EventAggrRoot genCreateSettlementFlowMonitorEvent() {
        Long eventId = DefaultSerialGenerator
                .getInstance()
                .genEventId();
        return EventAggrRootFactory
                .builder()
                .coreBuilder()
                .id(eventId)
                .type(EventTypeEnum.CREATE_SETTLEMENT_FLOW_MONITOR)
                .associatedSn(fundBatchAggrRoot.getIdStr())
                .optionalBuilder()
                .content(EventContentVO
                        .builder()
                        .build())
                .ext(EventExtVO
                        .builder()
                        .mqKey(fundBatchAggrRoot.getMerchantSn())
                        .build())
                .build();
    }
}
