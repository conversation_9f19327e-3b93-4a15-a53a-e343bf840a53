package com.shouqianba.trade.fund.clearing.infrastructure.repository.domain.converter;

import com.shouqianba.trade.fund.clearing.common.exception.FundClearingBizException;
import com.shouqianba.trade.fund.clearing.common.exception.enums.FundClearingRespCodeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.FundBillAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.FundBillAggrRootFactory;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.enums.FundBillStatusEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.enums.FundBillTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.vo.FundBillAmountVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.vo.FundBillBizDomainVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.vo.FundBillExtVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.vo.FundBillTradeDomainVO;
import com.shouqianba.trade.fund.clearing.domain.enums.BillSourceEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.sharingrule.model.enums.AccountTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.model.PayeeInfoVO;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.datasource.mysql.fundbill.po.FundBillPO;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR> Date: 2025/04/21 Time: 9:34 AM
 */
@Component
public class FundBillAggrRootConverter {

    public FundBillPO toFundBillPO(FundBillAggrRoot aggrRoot) {
        if (Objects.isNull(aggrRoot) || aggrRoot.isNotExist()) {
            throw new FundClearingBizException(FundClearingRespCodeEnum.FUND_BILL_NOT_EXIST);
        }
        return new FundBillPO()
                .setId(aggrRoot.getId())
                .setAcquiringDate(aggrRoot.getAcquiringDate())
                .setBillDate(aggrRoot.getBillDate())
                .setPayeeType(aggrRoot.getPayeeType().getCode())
                .setBrandSn(aggrRoot.getBrandSn())
                .setMerchantSn(aggrRoot.getMerchantSn())
                .setStoreSn(aggrRoot.getStoreSn())
                .setPayeeInfo(aggrRoot.getPayeeInfo() != null ? aggrRoot.getPayeeInfo().toJsonNode() : null)
                .setBillSource(aggrRoot.getBillSource() != null ? aggrRoot.getBillSource().getCode() : null)
                .setType(aggrRoot.getType() != null ? aggrRoot.getType().getCode() : null)
                .setStatus(aggrRoot.getStatus().getCode())
                .setAmount(aggrRoot.getAmount().toJsonNode())
                .setTransSn(aggrRoot.getTransSn())
                .setOrderSn(aggrRoot.getOrderSn())
                .setTradeDomain(aggrRoot.getTradeDomain() != null ? aggrRoot.getTradeDomain().toJsonNode() : null)
                .setBizDomain(aggrRoot.getBizDomain() != null ? aggrRoot.getBizDomain().toJsonNode() : null)
                .setExt(aggrRoot.getExt().toJsonNode())
                .setCtime(aggrRoot.getCreated())
                .setMtime(aggrRoot.getUpdated())
                .setVersion(aggrRoot.getVersion())
                .setFundBatchId(aggrRoot.getFundBatchId())
                .setFundPoolId(aggrRoot.getFundPoolId());
    }

    public FundBillAggrRoot toFundBillAggrRoot(FundBillPO po) {
        if (Objects.isNull(po)) {
            return FundBillAggrRoot.newEmptyInstance();
        }

        try {
            return FundBillAggrRootFactory.builder()
                    .coreBuilder()
                    .id(po.getId())
                    .acquiringDate(po.getAcquiringDate())
                    .billDate(po.getBillDate())
                    .payeeType(AccountTypeEnum.ofCode(po.getPayeeType()))
                    .brandSn(po.getBrandSn())
                    .merchantSn(po.getMerchantSn())
                    .storeSn(po.getStoreSn())
                    .payeeInfo(PayeeInfoVO.genFromJsonObject(po.getPayeeInfo(), PayeeInfoVO.class))
                    .billSource(BillSourceEnum.ofCode(po.getBillSource()))
                    .fundPoolId(po.getFundPoolId())
                    .funBatchId(po.getFundBatchId())
                    .type(FundBillTypeEnum.ofCode(po.getType()))
                    .status(FundBillStatusEnum.ofCode(po.getStatus()))
                    .amount(FundBillAmountVO.genFromJsonObject(po.getAmount(), FundBillAmountVO.class))
                    .transSn(po.getTransSn())
                    .tradeDomain(FundBillTradeDomainVO.genFromJsonObject(po.getTradeDomain(), FundBillTradeDomainVO.class))
                    .optionalBuilder()
                    .orderSn(po.getOrderSn())
                    .bizDomain(FundBillBizDomainVO.genFromJsonObject(po.getBizDomain(), FundBillBizDomainVO.class))
                    .ext(FundBillExtVO.genFromJsonObject(po.getExt(), FundBillExtVO.class))
                    .created(po.getCtime())
                    .updated(po.getMtime())
                    .version(po.getVersion())
                    .rebuild();
        } catch (Exception ignore) {
            return FundBillAggrRoot.newEmptyInstance();
        }
    }


}
