package com.shouqianba.trade.fund.clearing.infrastructure.support.bt.util;


import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.enums.FundBillStatusEnum;
import com.shouqianba.trade.fund.clearing.infrastructure.support.bt.action.entry.BillEntryInfoShareActionFunction;
import com.shouqianba.trade.fund.clearing.infrastructure.support.bt.action.entry.CreateFundPoolAndBatchIfNotExistsFunction;
import com.shouqianba.trade.fund.clearing.infrastructure.support.bt.action.entry.ExecuteBillEntryFunction;
import com.shouqianba.trade.fund.clearing.infrastructure.support.bt.blackbord.ClearingCustomBlackboard;
import com.shouqianba.trade.fund.clearing.infrastructure.support.bt.condition.entry.IsEntryRuleValidPredicate;
import com.shouqianba.trade.fund.clearing.infrastructure.support.bt.condition.entry.IsReceiptBillValidPredicate;
import com.wosai.general.ds.bt.AbstractNode;
import com.wosai.general.ds.bt.Blackboard;
import com.wosai.general.ds.bt.RootNode;
import com.wosai.general.ds.bt.composite.StrictSequenceNode;
import com.wosai.general.ds.bt.leaf.AbstractConditionNode;
import com.wosai.general.ds.bt.leaf.DefaultActionNode;
import com.wosai.general.ds.bt.leaf.DefaultConditionNode;

import java.util.Map;

/**
 * <AUTHOR> Date: 2025/04/22 Time: 02:59AM
 */
public class BehaviorTreeBillEntryUtils {

    /**
     * 构建账单入账行为树
     * <p>
     * 收款单入账的完整业务流程，包含验证、资金池创建、入账等操作
     */
    public static AbstractNode<?, Blackboard> buildBillEntryBehaviorTree() {

        // 1. 根节点
        var rootNode = new RootNode.RootNodeBuilder<>()
                .id("1.0")
                .name("1.0 收款账单入账流程")
                .description("处理收款账单入账的完整业务流程")
                .build();

        var infoShareSequenceNode = new StrictSequenceNode.StrictSequenceNodeBuilder<>()
                .id("100")
                .alias("信息共享严格顺序节点")
                .description("信息共享严格顺序节点")
                .name("信息共享严格顺序节点")
                .build();

        var infoShareNode = new DefaultActionNode.DefaultActionNodeBuilder<ClearingCustomBlackboard>()
                .id("101")
                .name("信息共享")
                .description("信息共享")
                .build();
        infoShareNode.addAction(new BillEntryInfoShareActionFunction(
                Map.of("billIdOutKey", "billId",
                        "collectRuleBelongIdOutKey", "collectRuleBelongId")));

        // 2. 主流程序列节点
        var mainSequenceNode = new StrictSequenceNode.StrictSequenceNodeBuilder<>()
                .id("2.0")
                .name("2.0 收款账单入账流程")
                .description("按顺序执行收款账单入账的验证和处理步骤")
                .build();

        // 3. 服务参数验证序列节点
        var serviceValidationSequenceNode = new StrictSequenceNode.StrictSequenceNodeBuilder<>()
                .id("3.0")
                .name("3.0 收款账单入账服务参数验证流程")
                .description("验证收款账单入账相关参数")
                .build();

        // 3.1 账单验证节点
        var billValidationNode = new DefaultConditionNode.DefaultConditionNodeBuilder<ClearingCustomBlackboard>()
                .id("3.1")
                .name("3.1 账单验证")
                .description("验证收款账单信息是否有效")
                .mode(AbstractConditionNode.ConditionMode.AND)
                .build();
        billValidationNode.addCondition(new IsReceiptBillValidPredicate(
                Map.of("fundBillStatus",
                        FundBillStatusEnum.IN_ENTRY.getCodeString()
                        , "billIdInKey", "billId")));

        // 3.2 规则验证节点
        var ruleValidationNode = new DefaultConditionNode.DefaultConditionNodeBuilder<ClearingCustomBlackboard>()
                .id("3.2")
                .name("3.2 规则验证")
                .description("验证入账规则是否满足条件")
                .mode(AbstractConditionNode.ConditionMode.AND)
                .build();
        ruleValidationNode.addCondition(new IsEntryRuleValidPredicate(
                Map.of("collectRuleBelongIdInKey", "collectRuleBelongId")));

        // todo 清分完成时调用
        // 4. 资金池创建节点
        var createPoolNode = new DefaultActionNode.DefaultActionNodeBuilder<ClearingCustomBlackboard>()
                .id("4.0")
                .name("4.0 创建资金池")
                .description("不存在资金池则创建资金池")
                .build();
        createPoolNode.addAction(new CreateFundPoolAndBatchIfNotExistsFunction(
                Map.of("billIdInKey", "billId",
                        "poolIdOutKey", "poolId",
                        "collectRuleBelongIdInKey", "collectRuleBelongId")));


        // 6. 资金池单入账节点
        var poolEntryNode = new DefaultActionNode.DefaultActionNodeBuilder<ClearingCustomBlackboard>()
                .id("6.0")
                .name("6.0 资金池单入账")
                .description("执行资金池入账操作")
                .build();
        poolEntryNode.addAction(new ExecuteBillEntryFunction(
                Map.of("billIdInKey", "billId",
                        "poolIdInKey", "poolId",
                        "collectRuleBelongIdInKey", "collectRuleBelongId")));

        // 组装节点关系
        serviceValidationSequenceNode.addChild(billValidationNode);
        serviceValidationSequenceNode.addChild(ruleValidationNode);

        mainSequenceNode.addChild(serviceValidationSequenceNode);
        mainSequenceNode.addChild(createPoolNode);
        mainSequenceNode.addChild(poolEntryNode);
        infoShareSequenceNode.addChild(infoShareNode);
        infoShareSequenceNode.addChild(mainSequenceNode);
        rootNode.addChild(infoShareSequenceNode);

        return rootNode;
    }

    public static void main(String[] args) {
        var rootNode = buildBillEntryBehaviorTree();
        System.out.println(rootNode.toJsonString());
    }
}
