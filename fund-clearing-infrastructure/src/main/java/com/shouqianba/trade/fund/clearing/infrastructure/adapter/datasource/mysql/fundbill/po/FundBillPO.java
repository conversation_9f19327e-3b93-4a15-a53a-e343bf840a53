package com.shouqianba.trade.fund.clearing.infrastructure.adapter.datasource.mysql.fundbill.po;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR> Date: 2025/04/21 Time: 9:34 AM
 */
@Data
@Accessors(chain = true)
public class FundBillPO {

    private Long id;
    private Long fundPoolId;
    private Long fundBatchId;
    private LocalDate acquiringDate;
    private LocalDate billDate;
    private Byte payeeType;
    private String brandSn;
    private String merchantSn;
    private String storeSn;
    
    private JsonNode payeeInfo;
    private Byte billSource;
    private Byte type;
    private Byte status;
    private JsonNode amount;
    private String transSn;
    private String orderSn;
    private JsonNode tradeDomain;
    private JsonNode bizDomain;
    private JsonNode ext;
    private LocalDateTime ctime;
    private LocalDateTime mtime;
    private Long version;

}
