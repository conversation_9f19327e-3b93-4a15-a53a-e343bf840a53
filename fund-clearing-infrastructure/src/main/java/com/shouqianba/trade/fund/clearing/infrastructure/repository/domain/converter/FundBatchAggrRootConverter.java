package com.shouqianba.trade.fund.clearing.infrastructure.repository.domain.converter;

import com.shouqianba.trade.fund.clearing.common.exception.FundClearingBizException;
import com.shouqianba.trade.fund.clearing.common.exception.enums.FundClearingRespCodeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.model.FundBatchAggrRoot;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.model.FundBatchAggrRootFactory;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.model.enums.FundBatchStatusEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.model.vo.FundBatchAmountVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.model.vo.FundBatchBizDomainVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.model.vo.FundBatchExtVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.sharingrule.model.enums.AccountTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.enums.BillSourceEnum;
import com.shouqianba.trade.fund.clearing.domain.model.PayeeInfoVO;
import com.shouqianba.trade.fund.clearing.infrastructure.adapter.datasource.mysql.fundbatch.po.FundBatchPO;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR> Date: 2025/04/21 Time: 9:34 AM
 */
@Component
public class FundBatchAggrRootConverter {

    public FundBatchPO toFundBatchPO(FundBatchAggrRoot aggrRoot) {
        if (Objects.isNull(aggrRoot) || aggrRoot.isNotExist()) {
            throw new FundClearingBizException(FundClearingRespCodeEnum.FUND_BATCH_NOT_EXIST);
        }
        return new FundBatchPO()
                .setId(aggrRoot.getId())
                .setPayeeType(aggrRoot
                        .getPayeeType()
                        .getCode())
                .setBrandSn(aggrRoot.getBrandSn())
                .setMerchantSn(aggrRoot.getMerchantSn())
                .setStoreSn(aggrRoot.getStoreSn())
                .setPayeeInfo(aggrRoot.getPayeeInfo() != null ? aggrRoot
                        .getPayeeInfo()
                        .toJsonNode() : null)
                .setBillSource(aggrRoot.getBillSource() != null ? aggrRoot
                        .getBillSource()
                        .getCode() : null)
                .setAcquiringCompany(aggrRoot.getAcquiringCompany())
                .setSettleTime(aggrRoot.getSettleTime())
                .setStatus(aggrRoot
                        .getStatus()
                        .getCode())
                .setAmount(aggrRoot
                        .getAmount()
                        .toJsonNode())
                .setBizDomain(aggrRoot
                        .getBizDomain()
                        .toJsonNode())
                .setExt(aggrRoot
                        .getExt()
                        .toJsonNode())
                .setCtime(aggrRoot.getCreated())
                .setMtime(aggrRoot.getUpdated())
                .setVersion(aggrRoot.getVersion());
    }

    public FundBatchAggrRoot toFundBatchAggrRoot(FundBatchPO po) {
        if (Objects.isNull(po)) {
            return FundBatchAggrRoot.newEmptyInstance();
        }

        try {
            return FundBatchAggrRootFactory
                    .builder()
                    .coreBuilder()
                    .id(po.getId())
                    .payeeType(AccountTypeEnum.ofCode(po.getPayeeType()))
                    .brandSn(po.getBrandSn())
                    .merchantSn(po.getMerchantSn())
                    .storeSn(po.getStoreSn())
                    .payeeInfo(PayeeInfoVO.genFromJsonObject(po.getPayeeInfo(), PayeeInfoVO.class))
                    .billSource(BillSourceEnum.ofCode(po.getBillSource()))
                    .acquiringCompany(po.getAcquiringCompany())
                    .settleTime(po.getSettleTime())
                    .status(FundBatchStatusEnum.ofCode(po.getStatus()))
                    .amount(FundBatchAmountVO.genFromJsonObject(po.getAmount(), FundBatchAmountVO.class))
                    .bizDomain(FundBatchBizDomainVO.genFromJsonObject(po.getBizDomain(), FundBatchBizDomainVO.class))
                    .optionalBuilder()
                    .ext(FundBatchExtVO.genFromJsonObject(po.getExt(), FundBatchExtVO.class))
                    .created(po.getCtime())
                    .updated(po.getMtime())
                    .version(po.getVersion())
                    .rebuild();
        } catch (Exception ignore) {
            return FundBatchAggrRoot.newEmptyInstance();
        }
    }
}
