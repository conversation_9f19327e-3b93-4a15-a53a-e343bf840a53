package com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.shouqianba.trade.fund.clearing.common.exception.FundClearingBizException;
import com.shouqianba.trade.fund.clearing.common.exception.enums.FundClearingRespCodeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.BaseVO;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 */
@Getter
@Jacksonized
@Builder(toBuilder = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EventContentFundBatchBillEntryToPoolVO extends BaseVO<EventContentFundBatchBillEntryToPoolVO> {


    @NotEmpty(message = "brand_id不能为空")
    private String brandId;

    @NotEmpty(message = "date不能为空")
    private String date;

    @NotEmpty(message = "action_id不能为空")
    private String actionId;

    @NotEmpty(message = "client_sn不能为空")
    private String clientSn;

    @NotEmpty(message = "status不能为空")
    private Integer status;

    @NotEmpty(message = "start_time不能为空")
    private Long finishTime;

    @NotEmpty(message = "end_time不能为空")
    private String failMsg;

    @NotEmpty(message = "clearing_result_file不能为空")
    private String clearingResultFile;

    @Override
    protected EventContentFundBatchBillEntryToPoolVO doReplaceNotNull(EventContentFundBatchBillEntryToPoolVO vo) {
        throw new FundClearingBizException(FundClearingRespCodeEnum.UNSUPPORTED_OPERATION_EXCEPTION);
    }

    public Long getClientSnNum() {
        if (clientSn == null) {
            return null;
        }
        return Long.parseLong(clientSn);
    }

    public boolean isClearingSuccess() {
        return status == 1;
    }

}