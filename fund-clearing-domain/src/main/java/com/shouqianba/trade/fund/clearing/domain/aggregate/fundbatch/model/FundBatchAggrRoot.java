package com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.model;

import com.shouqianba.trade.fund.clearing.common.exception.FundClearingBizException;
import com.shouqianba.trade.fund.clearing.common.exception.enums.FundClearingRespCodeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.BaseEntity;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.model.enums.FundBatchStatusEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.model.vo.FundBatchAmountVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.model.vo.FundBatchExtVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.model.vo.FundBatchBizDomainVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.sharingrule.model.enums.AccountTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.enums.BillSourceEnum;
import com.shouqianba.trade.fund.clearing.domain.model.PayeeInfoVO;
import lombok.AccessLevel;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

/**
 * <AUTHOR> Date: 2025/04/21 Time: 9:34 AM
 */
@Getter
@Setter(AccessLevel.PACKAGE)
@EqualsAndHashCode(callSuper = true)
public class FundBatchAggrRoot extends BaseEntity {

    public static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    @NotNull(message = "收款方主体类型不能为空")
    private AccountTypeEnum payeeType;

    private String brandSn;
    private String merchantSn;
    private String storeSn;

    @Valid
    private PayeeInfoVO payeeInfo;

    @NotNull(message = "账单来源不能为空")
    private BillSourceEnum billSource;

    @NotNull(message = "收单公司不能为空")
    private Integer acquiringCompany;

    @NotNull(message = "结算日期不能为空")
    private LocalDate settleTime;

    @NotNull(message = "批次状态不能为空")
    private FundBatchStatusEnum status;

    @Valid
    @NotNull(message = "金额不能为空")
    private FundBatchAmountVO amount;

    @Valid
    @NotNull(message = "业务领域信息不能为空")
    private FundBatchBizDomainVO bizDomain;

    @Valid
    @NotNull(message = "扩展字段不能为空")
    private FundBatchExtVO ext;

    protected FundBatchAggrRoot() {
    }

    public static FundBatchAggrRoot newEmptyInstance() {
        return new FundBatchAggrRoot();
    }

    public void checkExist() {
        if (isNotExist()) {
            throw new FundClearingBizException(FundClearingRespCodeEnum.FUND_BATCH_NOT_EXIST);
        }
    }

    @Override
    protected void setId(Long id) {
        super.id = id;
    }

    @Override
    protected void setCreated(LocalDateTime created) {
        super.created = created;
    }

    @Override
    protected void setUpdated(LocalDateTime updated) {
        super.updated = updated;
    }

    @Override
    protected void setVersion(Long version) {
        super.version = version;
    }

    public void updateStatus(FundBatchStatusEnum status) {
        if (Objects.nonNull(status)) {
            this.status = status;
            this.modifyUpdated();
        }
    }

    public Long getTotalAmount() {
        return amount.getTotalAmount();
    }

    public Long getFeeAmount() {
        return amount.getFeeAmount();
    }

    public Long getSettleAmount() {
        return amount.getSettleAmount();
    }

    public String getSettleTimeStr() {
        return settleTime.format(FORMATTER);
    }

    public void updateAmount(FundBatchAmountVO fundBatchAmountVO) {
        if (Objects.nonNull(fundBatchAmountVO)) {
            this.amount = fundBatchAmountVO;
            this.modifyUpdated();
        }
    }

    public void updateSettleBatchId(Long batchId) {
        if (Objects.nonNull(batchId)) {
            ext = ext.replaceNotNull(FundBatchExtVO
                    .builder()
                    .settleBatchId(batchId)
                    .build());
            this.modifyUpdated();
        }
    }

    public Long getSettleBatchId() {
        return ext.getSettleBatchId();
    }
}
