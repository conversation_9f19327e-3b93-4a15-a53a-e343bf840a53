package com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model;

import com.shouqianba.trade.fund.clearing.common.exception.FundClearingBizException;
import com.shouqianba.trade.fund.clearing.common.exception.enums.FundClearingRespCodeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.BaseFactory;
import com.shouqianba.trade.fund.clearing.domain.aggregate.sharingrule.model.enums.AccountTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.enums.FundBillStatusEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.enums.FundBillTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.vo.FundBillAmountVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.vo.FundBillBizDomainVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.vo.FundBillExtVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.vo.FundBillTradeDomainVO;
import com.shouqianba.trade.fund.clearing.domain.enums.BillSourceEnum;
import com.shouqianba.trade.fund.clearing.domain.model.PayeeInfoVO;
import com.wosai.general.util.validation.ValidationUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR> Date: 2025/04/21 Time: 9:34 AM
 */
public class FundBillAggrRootFactory extends BaseFactory {
    public static FundBillAggrRootBuilder builder() {
        return new FundBillAggrRootBuilder(FundBillAggrRoot.newEmptyInstance());
    }

    public static class FundBillAggrRootBuilder extends BaseBuilder<FundBillAggrRoot, FundBillAggrRootCoreBuilder, FundBillAggrRootOptionalBuilder> {
        private FundBillAggrRootCoreBuilder fundBillAggrRootCoreBuilder;
        private FundBillAggrRootOptionalBuilder fundBillAggrRootOptionalBuilder;

        protected FundBillAggrRootBuilder(FundBillAggrRoot fundBillAggrRoot) {
            super(fundBillAggrRoot);
        }

        @Override
        public FundBillAggrRootCoreBuilder coreBuilder() {
            if (Objects.isNull(fundBillAggrRootCoreBuilder)) {
                fundBillAggrRootCoreBuilder = new FundBillAggrRootCoreBuilder(aggrRoot);
            }
            return fundBillAggrRootCoreBuilder;
        }

        @Override
        public FundBillAggrRootOptionalBuilder optionalBuilder() {
            if (Objects.isNull(fundBillAggrRootOptionalBuilder)) {
                fundBillAggrRootOptionalBuilder = new FundBillAggrRootOptionalBuilder(aggrRoot);
            }
            return fundBillAggrRootOptionalBuilder;
        }

        @Override
        protected void checkParams() {
            ValidationUtils.ValidationResult result = ValidationUtils.validate(aggrRoot);
            if (result.isInvalid()) {
                throw new FundClearingBizException(FundClearingRespCodeEnum.ILLEGAL_ARGUMENT
                        , result.getMsg());
            }
        }
    }

    public static class FundBillAggrRootCoreBuilder extends FundBillAggrRootBuilder implements BaseCoreBuilder {

        protected FundBillAggrRootCoreBuilder(FundBillAggrRoot fundBillAggrRoot) {
            super(fundBillAggrRoot);
        }

        public FundBillAggrRootCoreBuilder id(Long id) {
            aggrRoot.setId(id);
            return this;
        }

        public FundBillAggrRootCoreBuilder acquiringDate(LocalDate acquiringDate) {
            aggrRoot.setAcquiringDate(acquiringDate);
            return this;
        }

        public FundBillAggrRootCoreBuilder billDate(LocalDate billDate) {
            aggrRoot.setBillDate(billDate);
            return this;
        }

        public FundBillAggrRootCoreBuilder payeeType(AccountTypeEnum payeeType) {
            aggrRoot.setPayeeType(payeeType);
            return this;
        }

        public FundBillAggrRootCoreBuilder brandSn(String brandSn) {
            aggrRoot.setBrandSn(brandSn);
            return this;
        }
        
        public FundBillAggrRootCoreBuilder merchantSn(String merchantSn) {
            aggrRoot.setMerchantSn(merchantSn);
            return this;
        }
        
        public FundBillAggrRootCoreBuilder storeSn(String storeSn) {
            aggrRoot.setStoreSn(storeSn);
            return this;
        }
        
        public FundBillAggrRootCoreBuilder payeeInfo(PayeeInfoVO payeeInfo) {
            aggrRoot.setPayeeInfo(payeeInfo);
            return this;
        }

        public FundBillAggrRootCoreBuilder billSource(BillSourceEnum billSource) {
            aggrRoot.setBillSource(billSource);
            return this;
        }

        public FundBillAggrRootCoreBuilder type(FundBillTypeEnum type) {
            aggrRoot.setType(type);
            return this;
        }

        public FundBillAggrRootCoreBuilder status(FundBillStatusEnum status) {
            aggrRoot.setStatus(status);
            return this;
        }

        public FundBillAggrRootCoreBuilder amount(FundBillAmountVO amount) {
            aggrRoot.setAmount(amount);
            return this;
        }

        public FundBillAggrRootCoreBuilder transSn(String transSn) {
            aggrRoot.setTransSn(transSn);
            return this;
        }

        public FundBillAggrRootCoreBuilder tradeDomain(FundBillTradeDomainVO tradeDomain) {
            aggrRoot.setTradeDomain(tradeDomain);
            return this;
        }

        public FundBillAggrRootCoreBuilder fundPoolId(Long funPoolId) {
            aggrRoot.setFundPoolId(funPoolId);
            return this;
        }

        public FundBillAggrRootCoreBuilder funBatchId(Long funBatchId) {
            aggrRoot.setFundBatchId(funBatchId);
            return this;
        }

    }

    public static class FundBillAggrRootOptionalBuilder extends FundBillAggrRootBuilder implements BaseOptionalBuilder {

        protected FundBillAggrRootOptionalBuilder(FundBillAggrRoot fundBillAggrRoot) {
            super(fundBillAggrRoot);
        }

        public FundBillAggrRootOptionalBuilder orderSn(String orderSn) {
            aggrRoot.setOrderSn(orderSn);
            return this;
        }

        public FundBillAggrRootOptionalBuilder bizDomain(FundBillBizDomainVO bizDomain) {
            aggrRoot.setBizDomain(bizDomain);
            return this;
        }

        public FundBillAggrRootOptionalBuilder ext(FundBillExtVO ext) {
            aggrRoot.setExt(ext);
            return this;
        }

        public FundBillAggrRootOptionalBuilder created(LocalDateTime created) {
            aggrRoot.setCreated(created);
            return this;
        }

        public FundBillAggrRootOptionalBuilder updated(LocalDateTime updated) {
            aggrRoot.setUpdated(updated);
            return this;
        }

        public FundBillAggrRootOptionalBuilder version(Long version) {
            aggrRoot.setVersion(version);
            return this;
        }

        @Override
        public void initOptional() {
            String orderSn = aggrRoot.getOrderSn();
            FundBillBizDomainVO bizDomain = aggrRoot.getBizDomain();
            FundBillExtVO ext = aggrRoot.getExt();
            LocalDateTime created = aggrRoot.getCreated();
            LocalDateTime updated = aggrRoot.getUpdated();
            Long version = aggrRoot.getVersion();

            LocalDateTime currentDateTime = LocalDateTime.now();
            if (Objects.isNull(orderSn)) {
                aggrRoot.setOrderSn(StringUtils.EMPTY);
            }
            if (Objects.isNull(bizDomain)) {
                aggrRoot.setBizDomain(FundBillBizDomainVO.newEmptyInstance());
            }
            if (Objects.isNull(ext)) {
                aggrRoot.setExt(FundBillExtVO.newEmptyInstance(aggrRoot.getIdStr()));
            }
            if (Objects.isNull(created)) {
                aggrRoot.setCreated(currentDateTime);
            }
            if (Objects.isNull(updated)) {
                aggrRoot.setUpdated(currentDateTime);
            }
            if (Objects.isNull(version)) {
                aggrRoot.setVersion(0L);
            }
        }
    }
}
