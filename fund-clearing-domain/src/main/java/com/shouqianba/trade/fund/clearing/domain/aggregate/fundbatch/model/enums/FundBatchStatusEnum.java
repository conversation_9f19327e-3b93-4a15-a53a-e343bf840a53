package com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.model.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Objects;

/**
 * 账单处理状态枚举
 * <AUTHOR> Date: 2025/04/21 Time: 9:34 AM
 */
@Getter
public enum FundBatchStatusEnum {
    ENTERING((byte) 1, "入账中"),
    SETTLING((byte) 2, "入账结算中"),
    SUCCESS((byte) 3, "入账结算成功"),
    POOL_CREATE_SUCCEEDED((byte) 5, "资金池创建成功"),
    FAIL((byte) 6, "入账结算失败");

    private final byte code;
    private final String desc;

    FundBatchStatusEnum(byte code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonValue
    public byte getCode() {
        return code;
    }

    @JsonCreator
    public static FundBatchStatusEnum ofCode(Byte code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (FundBatchStatusEnum statusEnum : FundBatchStatusEnum.values()) {
            if (statusEnum.getCode() == code) {
                return statusEnum;
            }
        }
        return null;
    }

    public boolean isEntering() {
        return this == ENTERING;
    }

    public boolean isSettling() {
        return this == SETTLING;
    }

    public boolean isSuccess() {
        return this == SUCCESS;
    }

    public boolean isFail() {
        return this == FAIL;
    }

    public boolean isFinished() {
        return isSuccess() || isFail();
    }

    public boolean isProcessing() {
        return isEntering() || isSettling();
    }
}
