package com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.model;

import com.shouqianba.trade.fund.clearing.common.exception.FundClearingBizException;
import com.shouqianba.trade.fund.clearing.common.exception.enums.FundClearingRespCodeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.BaseFactory;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.model.enums.FundBatchStatusEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.model.vo.FundBatchAmountVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.model.vo.FundBatchBizDomainVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.model.vo.FundBatchExtVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.sharingrule.model.enums.AccountTypeEnum;
import com.shouqianba.trade.fund.clearing.domain.enums.BillSourceEnum;
import com.shouqianba.trade.fund.clearing.domain.model.PayeeInfoVO;
import com.wosai.general.util.validation.ValidationUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR> Date: 2025/04/21 Time: 9:34 AM
 */
public class FundBatchAggrRootFactory extends BaseFactory {
    public static FundBatchAggrRootBuilder builder() {
        return new FundBatchAggrRootBuilder(FundBatchAggrRoot.newEmptyInstance());
    }

    public static class FundBatchAggrRootBuilder extends BaseBuilder<FundBatchAggrRoot, FundBatchAggrRootCoreBuilder, FundBatchAggrRootOptionalBuilder> {
        private FundBatchAggrRootCoreBuilder fundBatchAggrRootCoreBuilder;
        private FundBatchAggrRootOptionalBuilder fundBatchAggrRootOptionalBuilder;

        protected FundBatchAggrRootBuilder(FundBatchAggrRoot fundBatchAggrRoot) {
            super(fundBatchAggrRoot);
        }

        @Override
        public FundBatchAggrRootCoreBuilder coreBuilder() {
            if (Objects.isNull(fundBatchAggrRootCoreBuilder)) {
                fundBatchAggrRootCoreBuilder = new FundBatchAggrRootCoreBuilder(aggrRoot);
            }
            return fundBatchAggrRootCoreBuilder;
        }

        @Override
        public FundBatchAggrRootOptionalBuilder optionalBuilder() {
            if (Objects.isNull(fundBatchAggrRootOptionalBuilder)) {
                fundBatchAggrRootOptionalBuilder = new FundBatchAggrRootOptionalBuilder(aggrRoot);
            }
            return fundBatchAggrRootOptionalBuilder;
        }

        @Override
        protected void checkParams() {
            ValidationUtils.ValidationResult result = ValidationUtils.validate(aggrRoot);
            if (result.isInvalid()) {
                throw new FundClearingBizException(FundClearingRespCodeEnum.ILLEGAL_ARGUMENT
                        , result.getMsg());
            }
        }
    }

    public static class FundBatchAggrRootCoreBuilder extends FundBatchAggrRootBuilder implements BaseCoreBuilder {

        protected FundBatchAggrRootCoreBuilder(FundBatchAggrRoot fundBatchAggrRoot) {
            super(fundBatchAggrRoot);
        }

        public FundBatchAggrRootCoreBuilder id(Long id) {
            aggrRoot.setId(id);
            return this;
        }

        public FundBatchAggrRootCoreBuilder payeeType(AccountTypeEnum payeeType) {
            aggrRoot.setPayeeType(payeeType);
            return this;
        }

        public FundBatchAggrRootCoreBuilder brandSn(String brandSn) {
            aggrRoot.setBrandSn(brandSn);
            return this;
        }

        public FundBatchAggrRootCoreBuilder merchantSn(String merchantSn) {
            aggrRoot.setMerchantSn(merchantSn);
            return this;
        }

        public FundBatchAggrRootCoreBuilder storeSn(String storeSn) {
            aggrRoot.setStoreSn(storeSn);
            return this;
        }

        public FundBatchAggrRootCoreBuilder payeeInfo(PayeeInfoVO payeeInfo) {
            aggrRoot.setPayeeInfo(payeeInfo);
            return this;
        }

        public FundBatchAggrRootCoreBuilder billSource(BillSourceEnum billSource) {
            aggrRoot.setBillSource(billSource);
            return this;
        }

        public FundBatchAggrRootCoreBuilder acquiringCompany(Integer acquiringCompany) {
            aggrRoot.setAcquiringCompany(acquiringCompany);
            return this;
        }

        public FundBatchAggrRootCoreBuilder settleTime(LocalDate settleTime) {
            aggrRoot.setSettleTime(settleTime);
            return this;
        }

        public FundBatchAggrRootCoreBuilder status(FundBatchStatusEnum status) {
            aggrRoot.setStatus(status);
            return this;
        }

        public FundBatchAggrRootCoreBuilder amount(FundBatchAmountVO amount) {
            aggrRoot.setAmount(amount);
            return this;
        }

        public FundBatchAggrRootCoreBuilder bizDomain(FundBatchBizDomainVO bizDomain) {
            aggrRoot.setBizDomain(bizDomain);
            return this;
        }
    }

    public static class FundBatchAggrRootOptionalBuilder extends FundBatchAggrRootBuilder implements BaseOptionalBuilder {

        protected FundBatchAggrRootOptionalBuilder(FundBatchAggrRoot fundBatchAggrRoot) {
            super(fundBatchAggrRoot);
        }

        public FundBatchAggrRootOptionalBuilder ext(FundBatchExtVO ext) {
            aggrRoot.setExt(ext);
            return this;
        }

        public FundBatchAggrRootOptionalBuilder created(LocalDateTime created) {
            aggrRoot.setCreated(created);
            return this;
        }

        public FundBatchAggrRootOptionalBuilder updated(LocalDateTime updated) {
            aggrRoot.setUpdated(updated);
            return this;
        }

        public FundBatchAggrRootOptionalBuilder version(Long version) {
            aggrRoot.setVersion(version);
            return this;
        }

        @Override
        public void initOptional() {
            FundBatchExtVO ext = aggrRoot.getExt();
            LocalDateTime created = aggrRoot.getCreated();
            LocalDateTime updated = aggrRoot.getUpdated();
            Long version = aggrRoot.getVersion();

            LocalDateTime currentDateTime = LocalDateTime.now();

            if (Objects.isNull(ext)) {
                aggrRoot.setExt(FundBatchExtVO.newEmptyInstance());
            }
            if (Objects.isNull(created)) {
                aggrRoot.setCreated(currentDateTime);
            }
            if (Objects.isNull(updated)) {
                aggrRoot.setUpdated(currentDateTime);
            }
            if (Objects.isNull(version)) {
                aggrRoot.setVersion(0L);
            }
        }
    }
}
