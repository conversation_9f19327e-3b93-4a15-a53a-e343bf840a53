package com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.shouqianba.trade.fund.clearing.domain.aggregate.BaseVO;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;

import java.util.Objects;

/**
 * <AUTHOR> Date: 2023/7/30 Time: 10:11
 */
@Getter
@Jacksonized
@Builder(toBuilder = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EventContentCreateSettlementFlowVO extends BaseVO<EventContentCreateSettlementFlowVO> {

    private Long settleBatchId;


    @Override
    protected EventContentCreateSettlementFlowVO doReplaceNotNull(EventContentCreateSettlementFlowVO vo) {
        EventContentCreateSettlementFlowVO.EventContentCreateSettlementFlowVOBuilder builder = toBuilder();

        Long settleBatchId = vo.getSettleBatchId();
        if (Objects.nonNull(settleBatchId)) {
            builder.settleBatchId(settleBatchId);
        }
        return builder.build();
    }

}
