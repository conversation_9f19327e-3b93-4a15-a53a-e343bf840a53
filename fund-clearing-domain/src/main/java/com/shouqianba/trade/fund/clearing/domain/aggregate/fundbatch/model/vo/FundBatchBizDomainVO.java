package com.shouqianba.trade.fund.clearing.domain.aggregate.fundbatch.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.shouqianba.trade.fund.clearing.domain.aggregate.BaseVO;
import com.shouqianba.trade.fund.clearing.domain.aggregate.fundpool.model.enums.SharingActionStatusEnum;
import com.shouqianba.trade.fund.clearing.domain.enums.PaywayEnum;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.jackson.Jacksonized;

import javax.validation.constraints.NotNull;
import java.util.Objects;

/**
 * <AUTHOR> Date: 2025/04/21 Time: 9:34 AM
 */
@Getter
@Builder(toBuilder = true)
@Jacksonized
@JsonInclude(JsonInclude.Include.NON_NULL)
@EqualsAndHashCode(callSuper = true)
public class FundBatchBizDomainVO extends BaseVO<FundBatchBizDomainVO> {

    @NotNull(message = "分账动作状态不能为空")
    private SharingActionStatusVO sharingActionStatus;

    private PaywayEnum payway;

    private final Long sharingBtId;

    public static FundBatchBizDomainVO newEmptyInstance() {
        return FundBatchBizDomainVO
                .builder()
                .sharingActionStatus(SharingActionStatusVO
                        .builder()
                        .build())
                .build();
    }

    @Override
    protected FundBatchBizDomainVO doReplaceNotNull(FundBatchBizDomainVO vo) {
        FundBatchBizDomainVO.FundBatchBizDomainVOBuilder builder = toBuilder();

        SharingActionStatusVO sharingActionStatus = vo.getSharingActionStatus();
        if (Objects.nonNull(sharingActionStatus)) {
            builder.sharingActionStatus(this.sharingActionStatus.replaceNotNull(sharingActionStatus));
        }
        if (Objects.nonNull(vo.getSharingBtId())) {
            builder.sharingBtId(vo.getSharingBtId());
        }

        if (Objects.nonNull(vo.getPayway())) {
            builder.payway(vo.getPayway());
        }
        return builder.build();
    }


    @Setter
    @Getter
    @Builder(toBuilder = true)
    @Jacksonized
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @EqualsAndHashCode(callSuper = true)
    public static class SharingActionStatusVO extends BaseVO<SharingActionStatusVO> {

        /**
         * 生成正向交易状态
         */
        private final SharingActionStatusEnum genPayTransactionStatus;

        /**
         * 生成正向交易状态
         */
        private final SharingActionStatusEnum updatePayTransactionStatus;

        /**
         * 生成逆向交易状态
         */
        private final SharingActionStatusEnum reverseTransactionStatus;

        /**
         * 生成分账账本状态
         */
        private final SharingActionStatusEnum sharingBookStatus;

        @Override
        protected SharingActionStatusVO doReplaceNotNull(SharingActionStatusVO vo) {
            SharingActionStatusVO.SharingActionStatusVOBuilder builder = toBuilder();

            SharingActionStatusEnum walletPayTransactionStatus = vo.getGenPayTransactionStatus();
            if (Objects.nonNull(walletPayTransactionStatus)) {
                builder.genPayTransactionStatus(walletPayTransactionStatus);
            }
            SharingActionStatusEnum updatePayTransactionStatus = vo.getUpdatePayTransactionStatus();
            if (Objects.nonNull(updatePayTransactionStatus)) {
                builder.updatePayTransactionStatus(updatePayTransactionStatus);
            }
            SharingActionStatusEnum reverseTransactionStatus = vo.getReverseTransactionStatus();
            if (Objects.nonNull(reverseTransactionStatus)) {
                builder.reverseTransactionStatus(reverseTransactionStatus);
            }
            SharingActionStatusEnum sharingBookStatus = vo.getSharingBookStatus();
            if (Objects.nonNull(sharingBookStatus)) {
                builder.sharingBookStatus(sharingBookStatus);
            }
            return builder.build();
        }
    }
}
