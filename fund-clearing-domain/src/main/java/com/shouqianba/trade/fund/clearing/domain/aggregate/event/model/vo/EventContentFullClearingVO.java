package com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.shouqianba.trade.fund.clearing.common.exception.FundClearingBizException;
import com.shouqianba.trade.fund.clearing.common.exception.enums.FundClearingRespCodeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.BaseVO;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;

/**
 * <AUTHOR>
 */
@Getter
@Jacksonized
@Builder(toBuilder = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EventContentFullClearingVO extends BaseVO<EventContentFullClearingVO> {

    @Override
    protected EventContentFullClearingVO doReplaceNotNull(EventContentFullClearingVO vo) {
        throw new FundClearingBizException(FundClearingRespCodeEnum.UNSUPPORTED_OPERATION_EXCEPTION);
    }
}