package com.shouqianba.trade.fund.clearing.domain.aggregate.fundbill.model.query;

import lombok.Builder;
import lombok.Getter;

import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> Date: 2025/04/21 Time: 9:34 AM
 */
@Getter
@Builder(toBuilder = true)
public class FundBillAggrQuery {

    private Long id;
    private List<Long> ids;
    private Long fundBatchId;
    private Long fundPoolId;
    private LocalDate acquiringDate;
    private LocalDate billDate;
    private Byte payeeType;
    private String brandSn;
    private String merchantSn;
    private String storeSn;
    private Byte billSource;
    private Byte type;
    private Byte status;
    private List<Byte> statusList;
    private String transSn;
    private String orderSn;
    private Long poolId;

    // 分页相关字段
    private String cursorField;
    private String endCursor;
    private Integer querySize;

    // 排序相关字段
    private String sortField;
    private boolean isDesc;

    public boolean isSingleIdQuery() {
        return Objects.nonNull(id);
    }
}
