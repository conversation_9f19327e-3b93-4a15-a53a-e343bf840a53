package com.shouqianba.trade.fund.clearing.domain.aggregate.event.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.shouqianba.trade.fund.clearing.common.exception.FundClearingBizException;
import com.shouqianba.trade.fund.clearing.common.exception.enums.FundClearingRespCodeEnum;
import com.shouqianba.trade.fund.clearing.domain.aggregate.BaseVO;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;

import javax.validation.constraints.NotEmpty;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;

/**
 * <AUTHOR>
 */
@Getter
@Jacksonized
@Builder(toBuilder = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EventContentFundChannelNotifyVO extends BaseVO<EventContentFundChannelNotifyVO> {

    @NotEmpty(message = "brand_id不能为空")
    private String brandId;

    @NotEmpty(message = "action_id不能为空")
    private String actionId;

    @NotEmpty(message = "settlement_channel不能为空")
    private Integer settlementChannel;

    @NotEmpty(message = "settlement_bank_account不能为空")
    private Long amount;

    @NotEmpty(message = "entry_time不能为空")
    private Long entryTime;

    @Override
    protected EventContentFundChannelNotifyVO doReplaceNotNull(EventContentFundChannelNotifyVO vo) {
        throw new FundClearingBizException(FundClearingRespCodeEnum.UNSUPPORTED_OPERATION_EXCEPTION);
    }

    public LocalDate genSettleTime() {
        if (entryTime == null) {
            return null;
        }
        return Instant
                .ofEpochMilli(entryTime)
                .atZone(ZoneId.systemDefault())  // 指定时区
                .toLocalDate();
    }
}